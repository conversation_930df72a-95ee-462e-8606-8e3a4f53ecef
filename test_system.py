"""
Test Script for Face Recognition Attendance System
Tests core functionality and system components
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import tkinter as tk
        print("✓ Tkinter imported successfully")
    except ImportError as e:
        print(f"✗ Tkinter import failed: {e}")
        return False
    
    try:
        import cv2
        print("✓ OpenCV imported successfully")
    except ImportError as e:
        print(f"✗ OpenCV import failed: {e}")
        return False
    
    try:
        import face_recognition
        print("✓ face_recognition imported successfully")
    except ImportError as e:
        print(f"✗ face_recognition import failed: {e}")
        return False
    
    try:
        import pandas as pd
        print("✓ Pandas imported successfully")
    except ImportError as e:
        print(f"✗ Pandas import failed: {e}")
        return False
    
    try:
        from PIL import Image, ImageTk
        print("✓ PIL imported successfully")
    except ImportError as e:
        print(f"✗ PIL import failed: {e}")
        return False
    
    return True

def test_project_structure():
    """Test if project structure is correct"""
    print("\nTesting project structure...")
    
    required_files = [
        "main.py",
        "config.py",
        "requirements.txt",
        "README.md"
    ]
    
    required_dirs = [
        "gui",
        "utils",
        "database",
        "faces",
        "attendance"
    ]
    
    for file in required_files:
        if Path(file).exists():
            print(f"✓ {file} exists")
        else:
            print(f"✗ {file} missing")
            return False
    
    for dir in required_dirs:
        if Path(dir).exists():
            print(f"✓ {dir}/ directory exists")
        else:
            print(f"✗ {dir}/ directory missing")
            return False
    
    return True

def test_config():
    """Test configuration file"""
    print("\nTesting configuration...")
    
    try:
        import config
        print("✓ Config module imported successfully")
        
        # Test required config variables
        required_vars = [
            'DATABASE_PATH',
            'FACES_DIR',
            'ATTENDANCE_DIR',
            'TOLERANCE',
            'CAMERA_INDEX',
            'WINDOW_WIDTH',
            'WINDOW_HEIGHT'
        ]
        
        for var in required_vars:
            if hasattr(config, var):
                print(f"✓ {var} configured")
            else:
                print(f"✗ {var} missing in config")
                return False
        
        return True
        
    except ImportError as e:
        print(f"✗ Config import failed: {e}")
        return False

def test_database():
    """Test database functionality"""
    print("\nTesting database...")
    
    try:
        from utils.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        print("✓ Database initialized successfully")
        
        # Test admin login
        admin_user = db_manager.verify_admin_login("admin", "admin123")
        if admin_user:
            print("✓ Default admin user exists")
        else:
            print("✗ Default admin user not found")
            return False
        
        # Test getting students (should be empty initially)
        students = db_manager.get_all_students()
        print(f"✓ Retrieved {len(students)} students from database")
        
        db_manager.close_connection()
        return True
        
    except Exception as e:
        print(f"✗ Database test failed: {e}")
        return False

def test_face_recognition():
    """Test face recognition components"""
    print("\nTesting face recognition...")
    
    try:
        from utils.face_encoder import FaceEncoder
        from utils.face_recognizer import FaceRecognizer
        
        face_encoder = FaceEncoder()
        print("✓ FaceEncoder initialized")
        
        face_recognizer = FaceRecognizer()
        print("✓ FaceRecognizer initialized")
        
        # Test recognition statistics
        stats = face_recognizer.get_recognition_statistics()
        print(f"✓ Recognition stats: {stats}")
        
        return True
        
    except Exception as e:
        print(f"✗ Face recognition test failed: {e}")
        return False

def test_camera():
    """Test camera functionality"""
    print("\nTesting camera...")
    
    try:
        from utils.helper import CameraManager
        
        camera_manager = CameraManager()
        
        if camera_manager.initialize_camera():
            print("✓ Camera initialized successfully")
            
            # Try to read a frame
            frame = camera_manager.read_frame()
            if frame is not None:
                print("✓ Camera frame captured successfully")
                print(f"✓ Frame shape: {frame.shape}")
            else:
                print("✗ Failed to capture camera frame")
                return False
            
            camera_manager.release_camera()
            print("✓ Camera released successfully")
            return True
        else:
            print("✗ Camera initialization failed")
            return False
            
    except Exception as e:
        print(f"✗ Camera test failed: {e}")
        return False

def test_gui_components():
    """Test GUI components"""
    print("\nTesting GUI components...")
    
    try:
        # Test if GUI modules can be imported
        from gui.login import LoginWindow
        from gui.main_window import MainWindow
        from gui.register import StudentRegistrationWindow
        from gui.recognize import AttendanceWindow
        from gui.reports import ReportsWindow
        from gui.admin_panel import AdminPanelWindow
        
        print("✓ All GUI modules imported successfully")
        return True
        
    except Exception as e:
        print(f"✗ GUI components test failed: {e}")
        return False

def test_utilities():
    """Test utility functions"""
    print("\nTesting utilities...")
    
    try:
        from utils.helper import DataValidator, DateTimeHelper, UIHelper
        from utils.logger import setup_logger
        
        # Test data validation
        valid, msg = DataValidator.validate_roll_number("TEST001")
        if valid:
            print("✓ Roll number validation works")
        else:
            print(f"✗ Roll number validation failed: {msg}")
        
        valid, msg = DataValidator.validate_student_name("John Doe")
        if valid:
            print("✓ Student name validation works")
        else:
            print(f"✗ Student name validation failed: {msg}")
        
        valid, msg = DataValidator.validate_email("<EMAIL>")
        if valid:
            print("✓ Email validation works")
        else:
            print(f"✗ Email validation failed: {msg}")
        
        # Test date helper
        current_date = DateTimeHelper.get_current_date_string()
        print(f"✓ Current date: {current_date}")
        
        # Test logger
        logger = setup_logger("TestLogger")
        logger.info("Test log message")
        print("✓ Logger setup successful")
        
        return True
        
    except Exception as e:
        print(f"✗ Utilities test failed: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    print("=" * 50)
    print("Face Recognition Attendance System - Test Suite")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Project Structure", test_project_structure),
        ("Configuration", test_config),
        ("Database", test_database),
        ("Face Recognition", test_face_recognition),
        ("Camera", test_camera),
        ("GUI Components", test_gui_components),
        ("Utilities", test_utilities)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 30}")
        print(f"Running {test_name} Test")
        print(f"{'-' * 30}")
        
        try:
            if test_func():
                print(f"✓ {test_name} test PASSED")
                passed += 1
            else:
                print(f"✗ {test_name} test FAILED")
        except Exception as e:
            print(f"✗ {test_name} test ERROR: {e}")
    
    print(f"\n{'=' * 50}")
    print(f"Test Results: {passed}/{total} tests passed")
    print(f"{'=' * 50}")
    
    if passed == total:
        print("🎉 All tests passed! System is ready to use.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    
    if success:
        print("\n🚀 You can now run the main application:")
        print("   python main.py")
    else:
        print("\n🔧 Please fix the issues before running the application.")
    
    input("\nPress Enter to exit...")
