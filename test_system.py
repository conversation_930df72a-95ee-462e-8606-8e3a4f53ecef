"""
Test Script for Face Recognition Attendance System
Tests core functionality and system components
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import tkinter as tk
        print("✓ Tkinter imported successfully")
    except ImportError as e:
        print(f"✗ Tkinter import failed: {e}")
        return False
    
    try:
        import cv2
        print("✓ OpenCV imported successfully")
    except ImportError as e:
        print(f"✗ OpenCV import failed: {e}")
        return False
    
    try:
        import face_recognition
        print("✓ face_recognition imported successfully")
    except ImportError as e:
        print(f"✗ face_recognition import failed: {e}")
        return False
    
    try:
        import pandas as pd
        print("✓ Pandas imported successfully")
    except ImportError as e:
        print(f"✗ Pandas import failed: {e}")
        return False
    
    try:
        from PIL import Image, ImageTk
        print("✓ PIL imported successfully")
    except ImportError as e:
        print(f"✗ PIL import failed: {e}")
        return False
    
    return True

def test_project_structure():
    """Test if project structure is correct"""
    print("\nTesting project structure...")
    
    required_files = [
        "main.py",
        "config.py",
        "requirements.txt",
        "README.md"
    ]
    
    required_dirs = [
        "gui",
        "utils",
        "database",
        "faces",
        "attendance"
    ]
    
    for file in required_files:
        if Path(file).exists():
            print(f"✓ {file} exists")
        else:
            print(f"✗ {file} missing")
            return False
    
    for dir in required_dirs:
        if Path(dir).exists():
            print(f"✓ {dir}/ directory exists")
        else:
            print(f"✗ {dir}/ directory missing")
            return False
    
    return True

def test_config():
    """Test configuration file"""
    print("\nTesting configuration...")
    
    try:
        import config
        print("✓ Config module imported successfully")
        
        # Test required config variables
        required_vars = [
            'DATABASE_PATH',
            'FACES_DIR',
            'ATTENDANCE_DIR',
            'TOLERANCE',
            'CAMERA_INDEX',
            'WINDOW_WIDTH',
            'WINDOW_HEIGHT'
        ]
        
        for var in required_vars:
            if hasattr(config, var):
                print(f"✓ {var} configured")
            else:
                print(f"✗ {var} missing in config")
                return False
        
        return True
        
    except ImportError as e:
        print(f"✗ Config import failed: {e}")
        return False

def test_database():
    """Test database functionality"""
    print("\nTesting database...")
    
    try:
        from utils.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        print("✓ Database initialized successfully")
        
        # Test admin login
        admin_user = db_manager.verify_admin_login("admin", "admin123")
        if admin_user:
            print("✓ Default admin user exists")
        else:
            print("✗ Default admin user not found")
            return False
        
        # Test getting students (should be empty initially)
        students = db_manager.get_all_students()
        print(f"✓ Retrieved {len(students)} students from database")
        
        db_manager.close_connection()
        return True
        
    except Exception as e:
        print(f"✗ Database test failed: {e}")
        return False

def test_face_recognition():
    """Test face recognition components"""
    print("\nTesting face recognition...")
    
    try:
        from utils.face_encoder import FaceEncoder
        from utils.face_recognizer import FaceRecognizer
        
        face_encoder = FaceEncoder()
        print("✓ FaceEncoder initialized")
        
        face_recognizer = FaceRecognizer()
        print("✓ FaceRecognizer initialized")
        
        # Test recognition statistics
        stats = face_recognizer.get_recognition_statistics()
        print(f"✓ Recognition stats: {stats}")
        
        return True
        
    except Exception as e:
        print(f"✗ Face recognition test failed: {e}")
        return False

def test_camera():
    """Test camera functionality"""
    print("\nTesting camera...")
    
    try:
        from utils.helper import CameraManager
        
        camera_manager = CameraManager()
        
        if camera_manager.initialize_camera():
            print("✓ Camera initialized successfully")
            
            # Try to read a frame
            frame = camera_manager.read_frame()
            if frame is not None:
                print("✓ Camera frame captured successfully")
                print(f"✓ Frame shape: {frame.shape}")
            else:
                print("✗ Failed to capture camera frame")
                return False
            
            camera_manager.release_camera()
            print("✓ Camera released successfully")
            return True
        else:
            print("✗ Camera initialization failed")
            return False
            
    except Exception as e:
        print(f"✗ Camera test failed: {e}")
        return False

def test_gui_components():
    """Test GUI components"""
    print("\nTesting GUI components...")
    
    try:
        # Test if GUI modules can be imported
        from gui.login import LoginWindow
        from gui.main_window import MainWindow
        from gui.register import StudentRegistrationWindow
        from gui.recognize import AttendanceWindow
        from gui.reports import ReportsWindow
        from gui.admin_panel import AdminPanelWindow
        
        print("✓ All GUI modules imported successfully")
        return True
        
    except Exception as e:
        print(f"✗ GUI components test failed: {e}")
        return False

def test_utilities():
    """Test utility functions"""
    print("\nTesting utilities...")
    
    try:
        from utils.helper import DataValidator, DateTimeHelper, UIHelper
        from utils.logger import setup_logger
        
        # Test data validation
        valid, msg = DataValidator.validate_roll_number("TEST001")
        if valid:
            print("✓ Roll number validation works")
        else:
            print(f"✗ Roll number validation failed: {msg}")
        
        valid, msg = DataValidator.validate_student_name("John Doe")
        if valid:
            print("✓ Student name validation works")
        else:
            print(f"✗ Student name validation failed: {msg}")
        
        valid, msg = DataValidator.validate_email("<EMAIL>")
        if valid:
            print("✓ Email validation works")
        else:
            print(f"✗ Email validation failed: {msg}")
        
        # Test date helper
        current_date = DateTimeHelper.get_current_date_string()
        print(f"✓ Current date: {current_date}")
        
        # Test logger
        logger = setup_logger("TestLogger")
        logger.info("Test log message")
        print("✓ Logger setup successful")
        
        return True
        
    except Exception as e:
        print(f"✗ Utilities test failed: {e}")
        return False

def test_advanced_features():
    """Test advanced features"""
    print("\nTesting advanced features...")

    try:
        # Test performance monitor
        from utils.performance_monitor import PerformanceMonitor
        perf_monitor = PerformanceMonitor()
        print("✓ Performance monitor initialized")

        # Test notification system
        from utils.notification_system import NotificationSystem
        notification_system = NotificationSystem()
        print("✓ Notification system initialized")

        # Test advanced face recognition
        from utils.advanced_face_recognition import AdvancedFaceRecognizer
        advanced_recognizer = AdvancedFaceRecognizer()
        print("✓ Advanced face recognizer initialized")

        return True

    except Exception as e:
        print(f"✗ Advanced features test failed: {e}")
        return False

def test_performance():
    """Test system performance"""
    print("\nTesting performance...")

    try:
        import time
        import numpy as np

        # Test face encoding performance
        from utils.face_encoder import FaceEncoder
        face_encoder = FaceEncoder()

        # Create a dummy image
        dummy_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)

        start_time = time.time()
        face_locations, face_encodings = face_encoder.detect_faces_in_image(dummy_image)
        processing_time = time.time() - start_time

        print(f"✓ Face detection completed in {processing_time:.3f} seconds")

        if processing_time < 2.0:  # Should complete within 2 seconds
            print("✓ Performance test passed")
            return True
        else:
            print("⚠️ Performance test warning: slow processing")
            return True  # Still pass but with warning

    except Exception as e:
        print(f"✗ Performance test failed: {e}")
        return False

def test_error_handling():
    """Test error handling and recovery"""
    print("\nTesting error handling...")

    try:
        from utils.database_manager import DatabaseManager
        from utils.face_recognizer import FaceRecognizer

        # Test database error handling
        db_manager = DatabaseManager()

        # Test with invalid data
        try:
            db_manager.add_student({})  # Empty data should be handled gracefully
            print("✓ Database error handling works")
        except Exception:
            print("✓ Database properly rejects invalid data")

        # Test face recognizer error handling
        face_recognizer = FaceRecognizer()

        # Test with invalid frame
        result = face_recognizer.recognize_faces_in_frame(None)
        if result == []:
            print("✓ Face recognizer handles invalid input")

        return True

    except Exception as e:
        print(f"✗ Error handling test failed: {e}")
        return False

def test_security():
    """Test security features"""
    print("\nTesting security...")

    try:
        from utils.database_manager import DatabaseManager
        from utils.helper import DataValidator

        db_manager = DatabaseManager()

        # Test password hashing
        password = "test123"
        hashed = db_manager.hash_password(password)
        if len(hashed) == 64:  # SHA-256 produces 64 character hex string
            print("✓ Password hashing works")
        else:
            print("✗ Password hashing failed")
            return False

        # Test input validation
        valid, msg = DataValidator.validate_roll_number("'; DROP TABLE students; --")
        if not valid:
            print("✓ SQL injection protection works")
        else:
            print("⚠️ Potential SQL injection vulnerability")

        # Test email validation
        valid, msg = DataValidator.validate_email("invalid-email")
        if not valid:
            print("✓ Email validation works")
        else:
            print("✗ Email validation failed")
            return False

        return True

    except Exception as e:
        print(f"✗ Security test failed: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    print("=" * 60)
    print("Face Recognition Attendance System - Advanced Test Suite")
    print("=" * 60)

    tests = [
        ("Imports", test_imports),
        ("Project Structure", test_project_structure),
        ("Configuration", test_config),
        ("Database", test_database),
        ("Face Recognition", test_face_recognition),
        ("Camera", test_camera),
        ("GUI Components", test_gui_components),
        ("Utilities", test_utilities),
        ("Advanced Features", test_advanced_features),
        ("Performance", test_performance),
        ("Error Handling", test_error_handling),
        ("Security", test_security)
    ]

    passed = 0
    total = len(tests)
    warnings = 0

    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"Running {test_name} Test")
        print(f"{'-' * 40}")

        try:
            result = test_func()
            if result is True:
                print(f"✓ {test_name} test PASSED")
                passed += 1
            elif result == "warning":
                print(f"⚠️ {test_name} test PASSED with warnings")
                passed += 1
                warnings += 1
            else:
                print(f"✗ {test_name} test FAILED")
        except Exception as e:
            print(f"✗ {test_name} test ERROR: {e}")
            import traceback
            traceback.print_exc()

    print(f"\n{'=' * 60}")
    print(f"Test Results: {passed}/{total} tests passed")
    if warnings > 0:
        print(f"Warnings: {warnings}")
    print(f"{'=' * 60}")

    if passed == total:
        if warnings == 0:
            print("🎉 All tests passed! System is fully functional and ready to use.")
        else:
            print("✅ All tests passed with some warnings. System is functional.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
        print("\n📋 Troubleshooting Tips:")
        print("1. Ensure all dependencies are installed: pip install -r requirements.txt")
        print("2. Check camera permissions and availability")
        print("3. Verify Python version is 3.7 or higher")
        print("4. Run installation script: python install.py")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    
    if success:
        print("\n🚀 You can now run the main application:")
        print("   python main.py")
    else:
        print("\n🔧 Please fix the issues before running the application.")
    
    input("\nPress Enter to exit...")
