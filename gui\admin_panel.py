"""
Admin Panel Window for Face Recognition Attendance System
Handles student management and system administration
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import config
from utils.database_manager import DatabaseManager
from utils.face_recognizer import FaceRecognizer
from utils.helper import <PERSON><PERSON><PERSON>el<PERSON>, DataValidator
from utils.logger import setup_logger, log_system_event

class AdminPanelWindow:
    """Admin panel window for system management"""
    
    def __init__(self, parent, user_data):
        self.parent = parent
        self.user_data = user_data
        self.db_manager = DatabaseManager()
        self.face_recognizer = FaceRecognizer()
        self.logger = setup_logger("AdminPanel")
        
        # Create admin panel window
        self.window = tk.Toplevel(parent)
        self.window.title("Admin Panel")
        self.window.geometry("1000x700")
        self.window.configure(bg=config.BACKGROUND_COLOR)
        
        # Center window
        UIHelper.center_window(self.window, 1000, 700)
        
        # Make window modal
        self.window.transient(parent)
        self.window.grab_set()
        
        # Setup UI
        self.setup_ui()
        
        # Load initial data
        self.refresh_students()
        
        # Handle window close
        self.window.protocol("WM_DELETE_WINDOW", self.on_close)
    
    def setup_ui(self):
        """Setup the admin panel UI"""
        # Main container
        main_frame = tk.Frame(self.window, bg=config.BACKGROUND_COLOR)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Title
        title_label = tk.Label(
            main_frame,
            text="Admin Panel",
            font=('Arial', 18, 'bold'),
            fg=config.THEME_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        title_label.pack(pady=(0, 20))
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Create tabs
        self.create_students_tab()
        self.create_system_tab()
        self.create_settings_tab()
    
    def create_students_tab(self):
        """Create students management tab"""
        # Students tab frame
        students_frame = tk.Frame(self.notebook, bg=config.BACKGROUND_COLOR)
        self.notebook.add(students_frame, text="Students Management")
        
        # Top controls
        controls_frame = tk.Frame(students_frame, bg=config.BACKGROUND_COLOR)
        controls_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Search frame
        search_frame = tk.Frame(controls_frame, bg=config.BACKGROUND_COLOR)
        search_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        search_label = tk.Label(
            search_frame,
            text="Search:",
            font=('Arial', 11),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        search_label.pack(side=tk.LEFT, padx=(0, 5))
        
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        search_entry = tk.Entry(
            search_frame,
            textvariable=self.search_var,
            font=('Arial', 11),
            relief=tk.FLAT,
            bd=1
        )
        search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        # Action buttons
        buttons_frame = tk.Frame(controls_frame, bg=config.BACKGROUND_COLOR)
        buttons_frame.pack(side=tk.RIGHT)
        
        refresh_btn = tk.Button(
            buttons_frame,
            text="Refresh",
            font=('Arial', 10),
            bg='orange',
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.refresh_students
        )
        refresh_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        edit_btn = tk.Button(
            buttons_frame,
            text="Edit Student",
            font=('Arial', 10),
            bg='blue',
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.edit_student
        )
        edit_btn.pack(side=tk.LEFT, padx=5)
        
        delete_btn = tk.Button(
            buttons_frame,
            text="Delete Student",
            font=('Arial', 10),
            bg='red',
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.delete_student
        )
        delete_btn.pack(side=tk.LEFT, padx=(5, 0))
        
        # Students treeview
        tree_frame = tk.Frame(students_frame, bg=config.BACKGROUND_COLOR)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        # Treeview columns
        columns = ('ID', 'Roll Number', 'Name', 'Class', 'Section', 'Email', 'Phone', 'Created')
        self.students_tree = ttk.Treeview(tree_frame, columns=columns, show='headings')
        
        # Configure columns
        column_widths = {'ID': 50, 'Roll Number': 100, 'Name': 150, 'Class': 80, 
                        'Section': 80, 'Email': 150, 'Phone': 120, 'Created': 120}
        
        for col in columns:
            self.students_tree.heading(col, text=col)
            self.students_tree.column(col, width=column_widths.get(col, 100))
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.students_tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.students_tree.xview)
        
        self.students_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.students_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Bind double-click to edit
        self.students_tree.bind('<Double-1>', lambda e: self.edit_student())
    
    def create_system_tab(self):
        """Create system information tab"""
        # System tab frame
        system_frame = tk.Frame(self.notebook, bg=config.BACKGROUND_COLOR)
        self.notebook.add(system_frame, text="System Info")
        
        # System info content
        info_frame = tk.LabelFrame(
            system_frame,
            text="System Information",
            font=('Arial', 12, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        info_frame.pack(fill=tk.X, padx=20, pady=20)
        
        # System stats
        self.system_info_label = tk.Label(
            info_frame,
            text="Loading system information...",
            font=('Arial', 11),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR,
            justify=tk.LEFT
        )
        self.system_info_label.pack(anchor=tk.W, padx=20, pady=20)
        
        # Database info
        db_frame = tk.LabelFrame(
            system_frame,
            text="Database Information",
            font=('Arial', 12, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        db_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        
        self.db_info_label = tk.Label(
            db_frame,
            text="Loading database information...",
            font=('Arial', 11),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR,
            justify=tk.LEFT
        )
        self.db_info_label.pack(anchor=tk.W, padx=20, pady=20)
        
        # Face recognition info
        fr_frame = tk.LabelFrame(
            system_frame,
            text="Face Recognition System",
            font=('Arial', 12, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        fr_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        
        self.fr_info_label = tk.Label(
            fr_frame,
            text="Loading face recognition information...",
            font=('Arial', 11),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR,
            justify=tk.LEFT
        )
        self.fr_info_label.pack(anchor=tk.W, padx=20, pady=20)
        
        # System actions
        actions_frame = tk.LabelFrame(
            system_frame,
            text="System Actions",
            font=('Arial', 12, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        actions_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        
        actions_buttons = tk.Frame(actions_frame, bg=config.BACKGROUND_COLOR)
        actions_buttons.pack(fill=tk.X, padx=20, pady=20)
        
        reload_faces_btn = tk.Button(
            actions_buttons,
            text="Reload Face Data",
            font=('Arial', 11),
            bg=config.THEME_COLOR,
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.reload_face_data
        )
        reload_faces_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        clear_cache_btn = tk.Button(
            actions_buttons,
            text="Clear Recognition Cache",
            font=('Arial', 11),
            bg='orange',
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.clear_recognition_cache
        )
        clear_cache_btn.pack(side=tk.LEFT, padx=10)
        
        backup_db_btn = tk.Button(
            actions_buttons,
            text="Backup Database",
            font=('Arial', 11),
            bg='green',
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.backup_database
        )
        backup_db_btn.pack(side=tk.LEFT, padx=(10, 0))
        
        # Load system information
        self.load_system_info()
    
    def create_settings_tab(self):
        """Create system settings tab"""
        # Settings tab frame
        settings_frame = tk.Frame(self.notebook, bg=config.BACKGROUND_COLOR)
        self.notebook.add(settings_frame, text="Settings")
        
        # Face recognition settings
        fr_settings_frame = tk.LabelFrame(
            settings_frame,
            text="Face Recognition Settings",
            font=('Arial', 12, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        fr_settings_frame.pack(fill=tk.X, padx=20, pady=20)

        # Library selection
        library_frame = tk.Frame(fr_settings_frame, bg=config.BACKGROUND_COLOR)
        library_frame.pack(fill=tk.X, padx=20, pady=10)

        library_label = tk.Label(
            library_frame,
            text="Recognition Library:",
            font=('Arial', 11),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        library_label.pack(side=tk.LEFT)

        self.library_var = tk.StringVar(value="DeepFace" if config.USE_DEEPFACE else "face_recognition")
        library_combo = ttk.Combobox(
            library_frame,
            textvariable=self.library_var,
            values=["DeepFace", "face_recognition"],
            state="readonly",
            width=15
        )
        library_combo.pack(side=tk.LEFT, padx=(10, 0))
        library_combo.bind('<<ComboboxSelected>>', self.on_library_change)

        # DeepFace specific settings
        self.deepface_frame = tk.LabelFrame(
            fr_settings_frame,
            text="DeepFace Settings",
            font=('Arial', 11, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        self.deepface_frame.pack(fill=tk.X, padx=20, pady=10)

        # DeepFace model selection
        model_frame = tk.Frame(self.deepface_frame, bg=config.BACKGROUND_COLOR)
        model_frame.pack(fill=tk.X, padx=10, pady=5)

        model_label = tk.Label(
            model_frame,
            text="Model:",
            font=('Arial', 10),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        model_label.pack(side=tk.LEFT)

        self.deepface_model_var = tk.StringVar(value=config.DEEPFACE_MODEL)
        model_combo = ttk.Combobox(
            model_frame,
            textvariable=self.deepface_model_var,
            values=["VGG-Face", "Facenet", "Facenet512", "OpenFace", "DeepFace", "DeepID", "ArcFace", "Dlib", "SFace"],
            state="readonly",
            width=15
        )
        model_combo.pack(side=tk.LEFT, padx=(10, 0))

        # DeepFace detector selection
        detector_frame = tk.Frame(self.deepface_frame, bg=config.BACKGROUND_COLOR)
        detector_frame.pack(fill=tk.X, padx=10, pady=5)

        detector_label = tk.Label(
            detector_frame,
            text="Detector:",
            font=('Arial', 10),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        detector_label.pack(side=tk.LEFT)

        self.deepface_detector_var = tk.StringVar(value=config.DEEPFACE_DETECTOR)
        detector_combo = ttk.Combobox(
            detector_frame,
            textvariable=self.deepface_detector_var,
            values=["opencv", "ssd", "dlib", "mtcnn", "retinaface", "mediapipe"],
            state="readonly",
            width=15
        )
        detector_combo.pack(side=tk.LEFT, padx=(10, 0))

        # Distance metric selection
        distance_frame = tk.Frame(self.deepface_frame, bg=config.BACKGROUND_COLOR)
        distance_frame.pack(fill=tk.X, padx=10, pady=5)

        distance_label = tk.Label(
            distance_frame,
            text="Distance Metric:",
            font=('Arial', 10),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        distance_label.pack(side=tk.LEFT)

        self.deepface_distance_var = tk.StringVar(value=config.DEEPFACE_DISTANCE_METRIC)
        distance_combo = ttk.Combobox(
            distance_frame,
            textvariable=self.deepface_distance_var,
            values=["cosine", "euclidean", "euclidean_l2"],
            state="readonly",
            width=15
        )
        distance_combo.pack(side=tk.LEFT, padx=(10, 0))
        
        # Tolerance setting
        tolerance_frame = tk.Frame(fr_settings_frame, bg=config.BACKGROUND_COLOR)
        tolerance_frame.pack(fill=tk.X, padx=20, pady=10)
        
        tolerance_label = tk.Label(
            tolerance_frame,
            text="Recognition Tolerance:",
            font=('Arial', 11),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        tolerance_label.pack(side=tk.LEFT)
        
        self.tolerance_var = tk.DoubleVar(value=config.TOLERANCE)
        tolerance_scale = tk.Scale(
            tolerance_frame,
            variable=self.tolerance_var,
            from_=0.1,
            to=1.0,
            resolution=0.1,
            orient=tk.HORIZONTAL,
            length=200,
            bg=config.BACKGROUND_COLOR
        )
        tolerance_scale.pack(side=tk.LEFT, padx=(10, 0))
        
        # Camera settings
        camera_settings_frame = tk.LabelFrame(
            settings_frame,
            text="Camera Settings",
            font=('Arial', 12, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        camera_settings_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        
        # Camera index
        camera_frame = tk.Frame(camera_settings_frame, bg=config.BACKGROUND_COLOR)
        camera_frame.pack(fill=tk.X, padx=20, pady=10)
        
        camera_label = tk.Label(
            camera_frame,
            text="Camera Index:",
            font=('Arial', 11),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        camera_label.pack(side=tk.LEFT)
        
        self.camera_var = tk.IntVar(value=config.CAMERA_INDEX)
        camera_spinbox = tk.Spinbox(
            camera_frame,
            textvariable=self.camera_var,
            from_=0,
            to=5,
            width=10,
            font=('Arial', 11)
        )
        camera_spinbox.pack(side=tk.LEFT, padx=(10, 0))
        
        # Save settings button
        save_settings_btn = tk.Button(
            settings_frame,
            text="Save Settings",
            font=('Arial', 12, 'bold'),
            bg=config.THEME_COLOR,
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.save_settings
        )
        save_settings_btn.pack(pady=20)

    def refresh_students(self):
        """Refresh students list"""
        try:
            # Clear existing items
            for item in self.students_tree.get_children():
                self.students_tree.delete(item)

            # Get all students
            students = self.db_manager.get_all_students()

            # Populate treeview
            for student in students:
                created_date = datetime.fromisoformat(student['created_at']).strftime("%Y-%m-%d")

                self.students_tree.insert('', tk.END, values=(
                    student['id'],
                    student['roll_number'],
                    student['full_name'],
                    student['class_name'],
                    student['section'] or '',
                    student['email'] or '',
                    student['phone'] or '',
                    created_date
                ))

        except Exception as e:
            self.logger.error(f"Failed to refresh students: {str(e)}")
            UIHelper.show_error_message("Error", "Failed to refresh students list")

    def on_search_change(self, *args):
        """Handle search text change"""
        search_text = self.search_var.get().lower()

        # Clear current selection
        for item in self.students_tree.get_children():
            self.students_tree.delete(item)

        try:
            # Get all students
            students = self.db_manager.get_all_students()

            # Filter students based on search
            filtered_students = []
            for student in students:
                if (search_text in student['roll_number'].lower() or
                    search_text in student['full_name'].lower() or
                    search_text in student['class_name'].lower()):
                    filtered_students.append(student)

            # Populate treeview with filtered results
            for student in filtered_students:
                created_date = datetime.fromisoformat(student['created_at']).strftime("%Y-%m-%d")

                self.students_tree.insert('', tk.END, values=(
                    student['id'],
                    student['roll_number'],
                    student['full_name'],
                    student['class_name'],
                    student['section'] or '',
                    student['email'] or '',
                    student['phone'] or '',
                    created_date
                ))

        except Exception as e:
            self.logger.error(f"Search error: {str(e)}")

    def edit_student(self):
        """Edit selected student"""
        selection = self.students_tree.selection()
        if not selection:
            UIHelper.show_warning_message("No Selection", "Please select a student to edit")
            return

        # Get selected student data
        item = self.students_tree.item(selection[0])
        student_id = item['values'][0]

        try:
            # Get full student data from database
            students = self.db_manager.get_all_students()
            student = next((s for s in students if s['id'] == student_id), None)

            if not student:
                UIHelper.show_error_message("Error", "Student not found")
                return

            # Open edit dialog
            EditStudentDialog(self.window, student, self.db_manager, self.refresh_students)

        except Exception as e:
            self.logger.error(f"Edit student error: {str(e)}")
            UIHelper.show_error_message("Error", f"Failed to edit student: {str(e)}")

    def delete_student(self):
        """Delete selected student"""
        selection = self.students_tree.selection()
        if not selection:
            UIHelper.show_warning_message("No Selection", "Please select a student to delete")
            return

        # Get selected student data
        item = self.students_tree.item(selection[0])
        student_id = item['values'][0]
        student_name = item['values'][2]

        # Confirm deletion
        if not UIHelper.ask_yes_no(
            "Confirm Deletion",
            f"Are you sure you want to delete student '{student_name}'?\n\nThis action cannot be undone."
        ):
            return

        try:
            # Delete student
            success = self.db_manager.delete_student(student_id)

            if success:
                UIHelper.show_info_message("Success", f"Student '{student_name}' has been deleted")
                self.refresh_students()

                # Reload face recognition data
                self.face_recognizer.reload_known_faces()

                log_system_event("STUDENT_DELETE", f"Student deleted: {student_name} (ID: {student_id})")
            else:
                UIHelper.show_error_message("Error", "Failed to delete student")

        except Exception as e:
            self.logger.error(f"Delete student error: {str(e)}")
            UIHelper.show_error_message("Error", f"Failed to delete student: {str(e)}")

    def load_system_info(self):
        """Load system information"""
        try:
            # System information
            system_info = f"""Application Version: 1.0
Python Version: {self.get_python_version()}
Current User: {self.user_data['full_name']} ({self.user_data['username']})
Login Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
System Status: Running"""

            self.system_info_label.config(text=system_info)

            # Database information
            students = self.db_manager.get_all_students()
            total_students = len(students)
            students_with_faces = len([s for s in students if s['face_encoding']])

            # Get attendance statistics
            stats = self.db_manager.get_attendance_statistics()

            db_info = f"""Database Path: {config.DATABASE_PATH}
Total Students: {total_students}
Students with Face Data: {students_with_faces}
Total Attendance Records: {len(stats.get('attendance_data', []))}
Database Status: Connected"""

            self.db_info_label.config(text=db_info)

            # Face recognition information
            fr_stats = self.face_recognizer.get_recognition_statistics()

            fr_info = f"""Known Faces: {fr_stats.get('known_faces_count', 0)}
Recognition Model: {fr_stats.get('model', 'N/A')}
Tolerance: {fr_stats.get('tolerance', 'N/A')}
Cache Entries: {fr_stats.get('cache_entries', 0)}
Status: Ready"""

            self.fr_info_label.config(text=fr_info)

        except Exception as e:
            self.logger.error(f"Failed to load system info: {str(e)}")

    def get_python_version(self):
        """Get Python version"""
        import sys
        return f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"

    def reload_face_data(self):
        """Reload face recognition data"""
        try:
            self.face_recognizer.reload_known_faces()
            UIHelper.show_info_message("Success", "Face recognition data reloaded successfully")
            self.load_system_info()  # Refresh info
            log_system_event("SYSTEM", "Face recognition data reloaded")
        except Exception as e:
            self.logger.error(f"Failed to reload face data: {str(e)}")
            UIHelper.show_error_message("Error", f"Failed to reload face data: {str(e)}")

    def clear_recognition_cache(self):
        """Clear recognition cache"""
        try:
            self.face_recognizer.clear_recognition_cache()
            UIHelper.show_info_message("Success", "Recognition cache cleared successfully")
            self.load_system_info()  # Refresh info
            log_system_event("SYSTEM", "Recognition cache cleared")
        except Exception as e:
            self.logger.error(f"Failed to clear cache: {str(e)}")
            UIHelper.show_error_message("Error", f"Failed to clear cache: {str(e)}")

    def backup_database(self):
        """Backup database"""
        try:
            import shutil
            from datetime import datetime

            # Generate backup filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"attendance_backup_{timestamp}.db"
            backup_path = config.DATABASE_DIR / backup_filename

            # Copy database file
            shutil.copy2(config.DATABASE_PATH, backup_path)

            UIHelper.show_info_message(
                "Backup Successful",
                f"Database backed up successfully!\nBackup file: {backup_path}"
            )

            log_system_event("BACKUP", f"Database backed up to {backup_path}")

        except Exception as e:
            self.logger.error(f"Database backup error: {str(e)}")
            UIHelper.show_error_message("Backup Error", f"Failed to backup database: {str(e)}")

    def on_library_change(self, event=None):
        """Handle face recognition library change"""
        try:
            selected_library = self.library_var.get()

            if selected_library == "DeepFace":
                self.deepface_frame.pack(fill=tk.X, padx=20, pady=10)
            else:
                self.deepface_frame.pack_forget()

        except Exception as e:
            self.logger.error(f"Library change error: {str(e)}")

    def save_settings(self):
        """Save system settings"""
        try:
            # Update face recognition tolerance
            new_tolerance = self.tolerance_var.get()

            # Update camera index (would require restart to take effect)
            new_camera_index = self.camera_var.get()

            # Get DeepFace settings
            use_deepface = self.library_var.get() == "DeepFace"
            deepface_model = self.deepface_model_var.get()
            deepface_detector = self.deepface_detector_var.get()
            deepface_distance = self.deepface_distance_var.get()

            # Update configuration (in a real implementation, you'd save to config file)
            config.TOLERANCE = new_tolerance
            config.CAMERA_INDEX = new_camera_index
            config.USE_DEEPFACE = use_deepface
            config.DEEPFACE_MODEL = deepface_model
            config.DEEPFACE_DETECTOR = deepface_detector
            config.DEEPFACE_DISTANCE_METRIC = deepface_distance

            # Update face recognizer if available
            try:
                if hasattr(self, 'face_recognizer'):
                    if hasattr(self.face_recognizer, 'update_tolerance'):
                        self.face_recognizer.update_tolerance(new_tolerance)
            except Exception as e:
                self.logger.warning(f"Could not update face recognizer: {e}")

            settings_summary = f"""Settings saved successfully!

Face Recognition Library: {self.library_var.get()}
Tolerance: {new_tolerance}
Camera Index: {new_camera_index}

DeepFace Settings:
- Model: {deepface_model}
- Detector: {deepface_detector}
- Distance Metric: {deepface_distance}

Note: Some changes require application restart to take effect."""

            UIHelper.show_info_message("Settings Saved", settings_summary)

            log_system_event("SETTINGS", f"Settings updated - Library: {self.library_var.get()}, Tolerance: {new_tolerance}, Camera: {new_camera_index}")

        except Exception as e:
            self.logger.error(f"Save settings error: {str(e)}")
            UIHelper.show_error_message("Error", f"Failed to save settings: {str(e)}")

    def on_close(self):
        """Handle window close"""
        self.window.destroy()
        log_system_event("WINDOW_CLOSE", "Admin Panel closed")

class EditStudentDialog:
    """Dialog for editing student information"""

    def __init__(self, parent, student_data, db_manager, refresh_callback):
        self.parent = parent
        self.student_data = student_data
        self.db_manager = db_manager
        self.refresh_callback = refresh_callback
        self.logger = setup_logger("EditStudentDialog")

        # Create dialog window
        self.window = tk.Toplevel(parent)
        self.window.title("Edit Student")
        self.window.geometry("400x500")
        self.window.configure(bg=config.BACKGROUND_COLOR)
        self.window.resizable(False, False)

        # Center window
        UIHelper.center_window(self.window, 400, 500)

        # Make window modal
        self.window.transient(parent)
        self.window.grab_set()

        # Setup UI
        self.setup_ui()

        # Load student data
        self.load_student_data()

    def setup_ui(self):
        """Setup edit dialog UI"""
        # Main frame
        main_frame = tk.Frame(self.window, bg=config.BACKGROUND_COLOR)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Title
        title_label = tk.Label(
            main_frame,
            text="Edit Student Information",
            font=('Arial', 14, 'bold'),
            fg=config.THEME_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        title_label.pack(pady=(0, 20))

        # Form fields
        self.create_form_field(main_frame, "Roll Number:", "roll_number", readonly=True)
        self.create_form_field(main_frame, "Full Name:", "full_name")
        self.create_form_field(main_frame, "Class:", "class_name")
        self.create_form_field(main_frame, "Section:", "section")
        self.create_form_field(main_frame, "Email:", "email")
        self.create_form_field(main_frame, "Phone:", "phone")
        self.create_form_field(main_frame, "Address:", "address", multiline=True)

        # Buttons
        button_frame = tk.Frame(main_frame, bg=config.BACKGROUND_COLOR)
        button_frame.pack(fill=tk.X, pady=(20, 0))

        save_btn = tk.Button(
            button_frame,
            text="Save Changes",
            font=('Arial', 11, 'bold'),
            bg=config.THEME_COLOR,
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.save_changes
        )
        save_btn.pack(side=tk.LEFT, padx=(0, 10), ipady=5, ipadx=15)

        cancel_btn = tk.Button(
            button_frame,
            text="Cancel",
            font=('Arial', 11),
            bg='gray',
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.window.destroy
        )
        cancel_btn.pack(side=tk.RIGHT, ipady=5, ipadx=15)

    def create_form_field(self, parent, label_text, field_name, readonly=False, multiline=False):
        """Create a form field"""
        # Field frame
        field_frame = tk.Frame(parent, bg=config.BACKGROUND_COLOR)
        field_frame.pack(fill=tk.X, pady=(0, 10))

        # Label
        label = tk.Label(
            field_frame,
            text=label_text,
            font=('Arial', 10, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        label.pack(anchor=tk.W, pady=(0, 5))

        # Entry widget
        if multiline:
            entry = tk.Text(
                field_frame,
                font=('Arial', 10),
                height=3,
                relief=tk.FLAT,
                bd=1,
                state=tk.DISABLED if readonly else tk.NORMAL
            )
        else:
            entry = tk.Entry(
                field_frame,
                font=('Arial', 10),
                relief=tk.FLAT,
                bd=1,
                state=tk.DISABLED if readonly else tk.NORMAL
            )

        entry.pack(fill=tk.X, ipady=5)

        # Store reference
        setattr(self, f"{field_name}_entry", entry)

    def load_student_data(self):
        """Load student data into form"""
        try:
            # Load data into fields
            self.roll_number_entry.config(state=tk.NORMAL)
            self.roll_number_entry.delete(0, tk.END)
            self.roll_number_entry.insert(0, self.student_data['roll_number'])
            self.roll_number_entry.config(state=tk.DISABLED)

            self.full_name_entry.delete(0, tk.END)
            self.full_name_entry.insert(0, self.student_data['full_name'])

            self.class_name_entry.delete(0, tk.END)
            self.class_name_entry.insert(0, self.student_data['class_name'])

            self.section_entry.delete(0, tk.END)
            self.section_entry.insert(0, self.student_data['section'] or '')

            self.email_entry.delete(0, tk.END)
            self.email_entry.insert(0, self.student_data['email'] or '')

            self.phone_entry.delete(0, tk.END)
            self.phone_entry.insert(0, self.student_data['phone'] or '')

            self.address_entry.delete("1.0", tk.END)
            self.address_entry.insert("1.0", self.student_data['address'] or '')

        except Exception as e:
            self.logger.error(f"Failed to load student data: {str(e)}")

    def save_changes(self):
        """Save changes to student"""
        try:
            # Get form data
            student_data = {
                'full_name': self.full_name_entry.get().strip(),
                'class_name': self.class_name_entry.get().strip(),
                'section': self.section_entry.get().strip(),
                'email': self.email_entry.get().strip(),
                'phone': self.phone_entry.get().strip(),
                'address': self.address_entry.get("1.0", tk.END).strip()
            }

            # Validate data
            errors = []

            if not student_data['full_name']:
                errors.append("Full name is required")
            else:
                valid, msg = DataValidator.validate_student_name(student_data['full_name'])
                if not valid:
                    errors.append(f"Full name: {msg}")

            if not student_data['class_name']:
                errors.append("Class is required")

            if student_data['email']:
                valid, msg = DataValidator.validate_email(student_data['email'])
                if not valid:
                    errors.append(f"Email: {msg}")

            if student_data['phone']:
                valid, msg = DataValidator.validate_phone(student_data['phone'])
                if not valid:
                    errors.append(f"Phone: {msg}")

            if errors:
                UIHelper.show_error_message("Validation Error", "\n".join(errors))
                return

            # Update student in database
            success = self.db_manager.update_student(self.student_data['id'], student_data)

            if success:
                UIHelper.show_info_message("Success", "Student information updated successfully")

                # Refresh parent list
                if self.refresh_callback:
                    self.refresh_callback()

                # Close dialog
                self.window.destroy()

                log_system_event("STUDENT_UPDATE", f"Student updated: {student_data['full_name']}")
            else:
                UIHelper.show_error_message("Error", "Failed to update student information")

        except Exception as e:
            self.logger.error(f"Save changes error: {str(e)}")
            UIHelper.show_error_message("Error", f"Failed to save changes: {str(e)}")
