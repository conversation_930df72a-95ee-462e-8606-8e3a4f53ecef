"""
Helper Utilities for Face Recognition Attendance System
Contains common utility functions and helpers
"""

import cv2
import tkinter as tk
from tkinter import messagebox, filedialog
from PIL import Image, ImageTk
import pandas as pd
from datetime import datetime, date, timedelta
import os
import re
import config
from utils.logger import setup_logger

class CameraManager:
    """Manages camera operations"""
    
    def __init__(self, camera_index=0):
        self.camera_index = camera_index
        self.cap = None
        self.logger = setup_logger("CameraManager")
        
    def initialize_camera(self):
        """Initialize camera"""
        try:
            self.cap = cv2.VideoCapture(self.camera_index)
            
            if not self.cap.isOpened():
                raise Exception(f"Cannot open camera {self.camera_index}")
            
            # Set camera properties
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, config.FRAME_WIDTH)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, config.FRAME_HEIGHT)
            self.cap.set(cv2.CAP_PROP_FPS, config.FPS)
            
            self.logger.info(f"Camera {self.camera_index} initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Camera initialization failed: {str(e)}")
            return False
    
    def read_frame(self):
        """Read frame from camera"""
        if self.cap is None or not self.cap.isOpened():
            return None
        
        ret, frame = self.cap.read()
        return frame if ret else None
    
    def release_camera(self):
        """Release camera resources"""
        if self.cap is not None:
            self.cap.release()
            self.cap = None
            self.logger.info("Camera released")
    
    def is_camera_available(self):
        """Check if camera is available"""
        return self.cap is not None and self.cap.isOpened()

class ImageProcessor:
    """Image processing utilities"""
    
    @staticmethod
    def resize_image_for_display(image, max_width=640, max_height=480):
        """Resize image for display while maintaining aspect ratio"""
        height, width = image.shape[:2]
        
        # Calculate scaling factor
        scale_w = max_width / width
        scale_h = max_height / height
        scale = min(scale_w, scale_h)
        
        # Calculate new dimensions
        new_width = int(width * scale)
        new_height = int(height * scale)
        
        # Resize image
        resized = cv2.resize(image, (new_width, new_height))
        return resized
    
    @staticmethod
    def convert_cv2_to_tkinter(cv2_image):
        """Convert OpenCV image to Tkinter PhotoImage"""
        # Convert BGR to RGB
        rgb_image = cv2.cvtColor(cv2_image, cv2.COLOR_BGR2RGB)
        
        # Convert to PIL Image
        pil_image = Image.fromarray(rgb_image)
        
        # Convert to PhotoImage
        photo = ImageTk.PhotoImage(pil_image)
        
        return photo
    
    @staticmethod
    def enhance_image_quality(image):
        """Enhance image quality for better face recognition"""
        # Convert to LAB color space
        lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        
        # Apply CLAHE to L channel
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        l = clahe.apply(l)
        
        # Merge channels
        enhanced = cv2.merge([l, a, b])
        
        # Convert back to BGR
        enhanced = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)
        
        return enhanced

class DataValidator:
    """Data validation utilities"""
    
    @staticmethod
    def validate_roll_number(roll_number):
        """Validate roll number format"""
        if not roll_number or not isinstance(roll_number, str):
            return False, "Roll number is required"
        
        # Remove whitespace
        roll_number = roll_number.strip()
        
        if len(roll_number) < 3:
            return False, "Roll number must be at least 3 characters"
        
        if len(roll_number) > 20:
            return False, "Roll number must be less than 20 characters"
        
        # Check for valid characters (alphanumeric, dash, underscore)
        if not re.match(r'^[a-zA-Z0-9_-]+$', roll_number):
            return False, "Roll number can only contain letters, numbers, dash, and underscore"
        
        return True, "Valid"
    
    @staticmethod
    def validate_student_name(name):
        """Validate student name"""
        if not name or not isinstance(name, str):
            return False, "Name is required"
        
        name = name.strip()
        
        if len(name) < 2:
            return False, "Name must be at least 2 characters"
        
        if len(name) > 100:
            return False, "Name must be less than 100 characters"
        
        # Check for valid characters (letters, spaces, dots, apostrophes)
        if not re.match(r"^[a-zA-Z\s.']+$", name):
            return False, "Name can only contain letters, spaces, dots, and apostrophes"
        
        return True, "Valid"
    
    @staticmethod
    def validate_email(email):
        """Validate email format"""
        if not email:
            return True, "Valid"  # Email is optional
        
        email = email.strip()
        
        # Basic email regex
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        
        if not re.match(email_pattern, email):
            return False, "Invalid email format"
        
        return True, "Valid"
    
    @staticmethod
    def validate_phone(phone):
        """Validate phone number"""
        if not phone:
            return True, "Valid"  # Phone is optional
        
        phone = phone.strip()
        
        # Remove common separators
        phone_digits = re.sub(r'[^\d]', '', phone)
        
        if len(phone_digits) < 10 or len(phone_digits) > 15:
            return False, "Phone number must be 10-15 digits"
        
        return True, "Valid"

class ReportGenerator:
    """Generate various reports"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.logger = setup_logger("ReportGenerator")
    
    def generate_daily_attendance_report(self, report_date=None):
        """Generate daily attendance report"""
        try:
            if report_date is None:
                report_date = date.today()
            
            # Get attendance data
            attendance_records = self.db_manager.get_attendance_by_date(report_date)
            
            # Convert to DataFrame
            if attendance_records:
                df = pd.DataFrame([dict(record) for record in attendance_records])
                
                # Format columns
                df['attendance_time'] = pd.to_datetime(df['attendance_time']).dt.strftime('%H:%M:%S')
                df = df[['roll_number', 'full_name', 'class_name', 'section', 'attendance_time', 'status']]
                df.columns = ['Roll Number', 'Student Name', 'Class', 'Section', 'Time', 'Status']
                
            else:
                df = pd.DataFrame(columns=['Roll Number', 'Student Name', 'Class', 'Section', 'Time', 'Status'])
            
            return df
            
        except Exception as e:
            self.logger.error(f"Daily report generation failed: {str(e)}")
            return pd.DataFrame()
    
    def generate_student_attendance_report(self, student_id, start_date=None, end_date=None):
        """Generate attendance report for specific student"""
        try:
            attendance_records = self.db_manager.get_attendance_by_student(student_id, start_date, end_date)
            
            if attendance_records:
                df = pd.DataFrame([dict(record) for record in attendance_records])
                
                # Format columns
                df['attendance_date'] = pd.to_datetime(df['attendance_date']).dt.strftime('%Y-%m-%d')
                df['attendance_time'] = pd.to_datetime(df['attendance_time']).dt.strftime('%H:%M:%S')
                df = df[['attendance_date', 'attendance_time', 'status']]
                df.columns = ['Date', 'Time', 'Status']
                
            else:
                df = pd.DataFrame(columns=['Date', 'Time', 'Status'])
            
            return df
            
        except Exception as e:
            self.logger.error(f"Student report generation failed: {str(e)}")
            return pd.DataFrame()
    
    def export_report_to_csv(self, dataframe, filename):
        """Export report to CSV file"""
        try:
            # Ensure attendance directory exists
            config.ATTENDANCE_DIR.mkdir(exist_ok=True)
            
            # Generate full file path
            file_path = config.ATTENDANCE_DIR / filename
            
            # Export to CSV
            dataframe.to_csv(file_path, index=False)
            
            self.logger.info(f"Report exported to: {file_path}")
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"Report export failed: {str(e)}")
            return None

class DateTimeHelper:
    """Date and time utility functions"""
    
    @staticmethod
    def get_current_date_string():
        """Get current date as string"""
        return datetime.now().strftime(config.ATTENDANCE_DATE_FORMAT)
    
    @staticmethod
    def get_current_datetime_string():
        """Get current datetime as string"""
        return datetime.now().strftime(config.ATTENDANCE_TIME_FORMAT)
    
    @staticmethod
    def parse_date_string(date_string):
        """Parse date string to date object"""
        try:
            return datetime.strptime(date_string, config.ATTENDANCE_DATE_FORMAT).date()
        except ValueError:
            return None
    
    @staticmethod
    def get_week_date_range():
        """Get current week date range"""
        today = date.today()
        start_of_week = today - timedelta(days=today.weekday())
        end_of_week = start_of_week + timedelta(days=6)
        return start_of_week, end_of_week
    
    @staticmethod
    def get_month_date_range():
        """Get current month date range"""
        today = date.today()
        start_of_month = today.replace(day=1)
        
        # Get last day of month
        if today.month == 12:
            end_of_month = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            end_of_month = today.replace(month=today.month + 1, day=1) - timedelta(days=1)
        
        return start_of_month, end_of_month

class UIHelper:
    """UI utility functions"""
    
    @staticmethod
    def center_window(window, width, height):
        """Center window on screen"""
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        window.geometry(f"{width}x{height}+{x}+{y}")
    
    @staticmethod
    def show_info_message(title, message):
        """Show info message box"""
        messagebox.showinfo(title, message)
    
    @staticmethod
    def show_error_message(title, message):
        """Show error message box"""
        messagebox.showerror(title, message)
    
    @staticmethod
    def show_warning_message(title, message):
        """Show warning message box"""
        messagebox.showwarning(title, message)
    
    @staticmethod
    def ask_yes_no(title, message):
        """Ask yes/no question"""
        return messagebox.askyesno(title, message)
    
    @staticmethod
    def select_file(title="Select File", filetypes=None):
        """Open file selection dialog"""
        if filetypes is None:
            filetypes = [("All files", "*.*")]
        
        return filedialog.askopenfilename(title=title, filetypes=filetypes)
    
    @staticmethod
    def select_save_file(title="Save File", defaultextension=".csv", filetypes=None):
        """Open save file dialog"""
        if filetypes is None:
            filetypes = [("CSV files", "*.csv"), ("All files", "*.*")]
        
        return filedialog.asksaveasfilename(
            title=title,
            defaultextension=defaultextension,
            filetypes=filetypes
        )
