"""
Logging utility for Face Recognition Attendance System
Provides centralized logging functionality
"""

import logging
import logging.handlers
from datetime import datetime
import config

def setup_logger(name="FaceAttendanceSystem", level=None):
    """
    Setup and configure logger for the application
    
    Args:
        name (str): Logger name
        level (str): Logging level
    
    Returns:
        logging.Logger: Configured logger instance
    """
    
    # Create logger
    logger = logging.getLogger(name)
    
    # Set level
    if level is None:
        level = getattr(logging, config.LOG_LEVEL.upper(), logging.INFO)
    logger.setLevel(level)
    
    # Avoid duplicate handlers
    if logger.handlers:
        return logger
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    simple_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )
    
    # File handler with rotation
    try:
        file_handler = logging.handlers.RotatingFileHandler(
            config.LOG_FILE,
            maxBytes=5*1024*1024,  # 5MB
            backupCount=5
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(detailed_formatter)
        logger.addHandler(file_handler)
    except Exception as e:
        print(f"Warning: Could not create file handler: {e}")
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(simple_formatter)
    logger.addHandler(console_handler)
    
    return logger

def log_attendance_event(student_name, roll_number, status="Present", confidence=None):
    """
    Log attendance marking event
    
    Args:
        student_name (str): Student's full name
        roll_number (str): Student's roll number
        status (str): Attendance status
        confidence (float): Recognition confidence score
    """
    logger = logging.getLogger("FaceAttendanceSystem")
    
    message = f"Attendance marked - Student: {student_name} ({roll_number}), Status: {status}"
    if confidence is not None:
        message += f", Confidence: {confidence:.2f}"
    
    logger.info(message)

def log_system_event(event_type, message, level="INFO"):
    """
    Log system events
    
    Args:
        event_type (str): Type of event (LOGIN, LOGOUT, ERROR, etc.)
        message (str): Event message
        level (str): Log level
    """
    logger = logging.getLogger("FaceAttendanceSystem")
    
    log_message = f"[{event_type}] {message}"
    
    if level.upper() == "DEBUG":
        logger.debug(log_message)
    elif level.upper() == "INFO":
        logger.info(log_message)
    elif level.upper() == "WARNING":
        logger.warning(log_message)
    elif level.upper() == "ERROR":
        logger.error(log_message)
    elif level.upper() == "CRITICAL":
        logger.critical(log_message)
    else:
        logger.info(log_message)

def log_face_recognition_event(event_type, details=None):
    """
    Log face recognition specific events
    
    Args:
        event_type (str): Type of event (DETECTION, RECOGNITION, ENCODING, etc.)
        details (dict): Additional event details
    """
    logger = logging.getLogger("FaceAttendanceSystem")
    
    message = f"Face Recognition - {event_type}"
    if details:
        detail_str = ", ".join([f"{k}: {v}" for k, v in details.items()])
        message += f" - {detail_str}"
    
    logger.info(message)

def log_database_event(operation, table, record_id=None, success=True, error=None):
    """
    Log database operations
    
    Args:
        operation (str): Database operation (INSERT, UPDATE, DELETE, SELECT)
        table (str): Table name
        record_id: Record ID if applicable
        success (bool): Operation success status
        error (str): Error message if operation failed
    """
    logger = logging.getLogger("FaceAttendanceSystem")
    
    message = f"Database {operation} on {table}"
    if record_id:
        message += f" (ID: {record_id})"
    
    if success:
        logger.info(f"{message} - SUCCESS")
    else:
        logger.error(f"{message} - FAILED: {error}")

class AttendanceLogger:
    """
    Specialized logger for attendance system events
    """
    
    def __init__(self):
        self.logger = setup_logger("AttendanceLogger")
    
    def log_student_registration(self, student_data, success=True, error=None):
        """Log student registration event"""
        if success:
            self.logger.info(f"Student registered: {student_data.get('full_name')} ({student_data.get('roll_number')})")
        else:
            self.logger.error(f"Student registration failed: {error}")
    
    def log_attendance_marking(self, student_name, roll_number, success=True, error=None, confidence=None):
        """Log attendance marking event"""
        if success:
            msg = f"Attendance marked for {student_name} ({roll_number})"
            if confidence:
                msg += f" with confidence {confidence:.2f}"
            self.logger.info(msg)
        else:
            self.logger.error(f"Attendance marking failed for {student_name} ({roll_number}): {error}")
    
    def log_face_detection(self, faces_detected, processing_time=None):
        """Log face detection event"""
        msg = f"Face detection completed - {faces_detected} face(s) detected"
        if processing_time:
            msg += f" in {processing_time:.2f}s"
        self.logger.debug(msg)
    
    def log_camera_event(self, event_type, success=True, error=None):
        """Log camera related events"""
        if success:
            self.logger.info(f"Camera {event_type} successful")
        else:
            self.logger.error(f"Camera {event_type} failed: {error}")
    
    def log_report_generation(self, report_type, date_range=None, success=True, error=None):
        """Log report generation events"""
        msg = f"Report generation - Type: {report_type}"
        if date_range:
            msg += f", Date range: {date_range}"
        
        if success:
            self.logger.info(f"{msg} - SUCCESS")
        else:
            self.logger.error(f"{msg} - FAILED: {error}")

# Global logger instance
attendance_logger = AttendanceLogger()
