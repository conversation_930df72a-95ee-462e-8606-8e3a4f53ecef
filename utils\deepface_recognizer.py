"""
DeepFace-based Face Recognition Engine for Face Recognition Attendance System
Advanced face recognition using DeepFace library with multiple models
"""

import cv2
import numpy as np
import pandas as pd
from deepface import DeepFace
import os
import pickle
from datetime import datetime, timedelta
import threading
import time
from collections import defaultdict
import config
from utils.database_manager import DatabaseManager
from utils.logger import setup_logger, log_face_recognition_event

class DeepFaceRecognizer:
    """Advanced face recognition using DeepFace library"""
    
    def __init__(self, model_name='VGG-Face', detector_backend='opencv', distance_metric='cosine'):
        self.logger = setup_logger("DeepFaceRecognizer")
        self.db_manager = DatabaseManager()
        
        # DeepFace configuration
        self.model_name = model_name  # VGG-Face, Facenet, OpenFace, DeepFace, DeepID, ArcFace, Dlib
        self.detector_backend = detector_backend  # opencv, ssd, dlib, mtcnn, retinaface
        self.distance_metric = distance_metric  # cosine, euclidean, euclidean_l2
        
        # Recognition settings
        self.confidence_threshold = config.ATTENDANCE_CONFIDENCE_THRESHOLD
        self.verification_threshold = 0.6  # Adjust based on model
        
        # Face database
        self.face_database_path = config.FACES_DIR / "deepface_db"
        self.face_database_path.mkdir(exist_ok=True)
        
        # Known faces cache
        self.known_faces_cache = {}
        self.cache_lock = threading.Lock()
        self.cache_last_updated = None
        
        # Recognition cache for performance
        self.recognition_cache = {}
        self.cache_timeout = 300  # 5 minutes
        
        # Performance tracking
        self.performance_stats = {
            'total_recognitions': 0,
            'successful_recognitions': 0,
            'average_processing_time': 0,
            'model_accuracy': 0.0
        }
        
        # Initialize models
        self._initialize_models()
        self._load_face_database()
    
    def _initialize_models(self):
        """Initialize DeepFace models"""
        try:
            self.logger.info(f"Initializing DeepFace with model: {self.model_name}")
            
            # Pre-load the model by running a dummy verification
            dummy_img = np.zeros((224, 224, 3), dtype=np.uint8)
            temp_path1 = "temp_dummy1.jpg"
            temp_path2 = "temp_dummy2.jpg"
            
            cv2.imwrite(temp_path1, dummy_img)
            cv2.imwrite(temp_path2, dummy_img)
            
            try:
                DeepFace.verify(
                    img1_path=temp_path1,
                    img2_path=temp_path2,
                    model_name=self.model_name,
                    detector_backend=self.detector_backend,
                    distance_metric=self.distance_metric,
                    enforce_detection=False
                )
                self.logger.info("DeepFace models loaded successfully")
            except Exception as e:
                self.logger.warning(f"Model pre-loading failed: {e}")
            finally:
                # Clean up temp files
                for temp_file in [temp_path1, temp_path2]:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                        
        except Exception as e:
            self.logger.error(f"DeepFace initialization failed: {e}")
            raise
    
    def _load_face_database(self):
        """Load face database from stored student data"""
        try:
            with self.cache_lock:
                self.known_faces_cache.clear()
                
                # Get all students with face data
                students = self.db_manager.get_all_students()
                
                for student in students:
                    if student['face_encoding']:
                        # Create student face directory
                        student_dir = self.face_database_path / student['roll_number']
                        student_dir.mkdir(exist_ok=True)
                        
                        # Save face image for DeepFace
                        face_image_path = student_dir / "face.jpg"
                        
                        if not face_image_path.exists():
                            # Decode and save face image from database
                            try:
                                face_encoding = pickle.loads(student['face_encoding'])
                                # If we have the original image path, copy it
                                if student.get('photo_path') and os.path.exists(student['photo_path']):
                                    import shutil
                                    shutil.copy2(student['photo_path'], face_image_path)
                                else:
                                    # Generate a placeholder or skip
                                    continue
                            except Exception as e:
                                self.logger.warning(f"Could not process face data for {student['roll_number']}: {e}")
                                continue
                        
                        # Store in cache
                        self.known_faces_cache[student['roll_number']] = {
                            'student_info': student,
                            'face_path': str(face_image_path)
                        }
                
                self.cache_last_updated = datetime.now()
                self.logger.info(f"Loaded {len(self.known_faces_cache)} faces into DeepFace database")
                
        except Exception as e:
            self.logger.error(f"Face database loading failed: {e}")
    
    def register_face(self, image_path_or_array, student_data):
        """Register a new face in the DeepFace database"""
        try:
            roll_number = student_data['roll_number']
            
            # Create student directory
            student_dir = self.face_database_path / roll_number
            student_dir.mkdir(exist_ok=True)
            
            # Save face image
            face_image_path = student_dir / "face.jpg"
            
            if isinstance(image_path_or_array, str):
                # Copy existing image
                import shutil
                shutil.copy2(image_path_or_array, face_image_path)
            else:
                # Save numpy array as image
                cv2.imwrite(str(face_image_path), image_path_or_array)
            
            # Verify the face can be detected
            try:
                face_analysis = DeepFace.analyze(
                    img_path=str(face_image_path),
                    actions=['age', 'gender', 'emotion'],
                    detector_backend=self.detector_backend,
                    enforce_detection=True
                )
                
                self.logger.info(f"Face registered successfully for {roll_number}")
                
                # Update cache
                with self.cache_lock:
                    self.known_faces_cache[roll_number] = {
                        'student_info': student_data,
                        'face_path': str(face_image_path)
                    }
                
                return True, face_analysis
                
            except Exception as e:
                self.logger.error(f"Face detection failed during registration: {e}")
                # Clean up failed registration
                if face_image_path.exists():
                    face_image_path.unlink()
                return False, str(e)
                
        except Exception as e:
            self.logger.error(f"Face registration failed: {e}")
            return False, str(e)
    
    def recognize_face(self, image_path_or_array, return_analysis=True):
        """Recognize face using DeepFace"""
        start_time = time.time()
        
        try:
            # Save input image temporarily if it's an array
            temp_image_path = None
            if isinstance(image_path_or_array, np.ndarray):
                temp_image_path = "temp_recognition.jpg"
                cv2.imwrite(temp_image_path, image_path_or_array)
                image_path = temp_image_path
            else:
                image_path = image_path_or_array
            
            # Check cache first
            image_hash = self._get_image_hash(image_path)
            if image_hash in self.recognition_cache:
                cache_entry = self.recognition_cache[image_hash]
                if time.time() - cache_entry['timestamp'] < self.cache_timeout:
                    return cache_entry['result']
            
            recognition_results = []
            
            # Analyze the input image
            if return_analysis:
                try:
                    analysis = DeepFace.analyze(
                        img_path=image_path,
                        actions=['age', 'gender', 'emotion', 'race'],
                        detector_backend=self.detector_backend,
                        enforce_detection=False
                    )
                except Exception as e:
                    self.logger.warning(f"Face analysis failed: {e}")
                    analysis = None
            else:
                analysis = None
            
            # Find matching faces
            best_match = None
            best_distance = float('inf')
            
            with self.cache_lock:
                for roll_number, face_data in self.known_faces_cache.items():
                    try:
                        # Verify against known face
                        result = DeepFace.verify(
                            img1_path=image_path,
                            img2_path=face_data['face_path'],
                            model_name=self.model_name,
                            detector_backend=self.detector_backend,
                            distance_metric=self.distance_metric,
                            enforce_detection=False
                        )
                        
                        # Check if it's a match
                        if result['verified'] and result['distance'] < best_distance:
                            best_distance = result['distance']
                            best_match = {
                                'student_info': face_data['student_info'],
                                'distance': result['distance'],
                                'confidence': (1 - result['distance']) * 100,
                                'model_name': self.model_name,
                                'threshold': result['threshold']
                            }
                    
                    except Exception as e:
                        self.logger.debug(f"Verification failed for {roll_number}: {e}")
                        continue
            
            # Prepare result
            result = {
                'recognized': best_match is not None,
                'student_info': best_match['student_info'] if best_match else None,
                'confidence': best_match['confidence'] if best_match else 0.0,
                'distance': best_match['distance'] if best_match else None,
                'model_name': self.model_name,
                'processing_time': time.time() - start_time,
                'analysis': analysis
            }
            
            # Cache the result
            self.recognition_cache[image_hash] = {
                'result': result,
                'timestamp': time.time()
            }
            
            # Update performance stats
            self.performance_stats['total_recognitions'] += 1
            if result['recognized']:
                self.performance_stats['successful_recognitions'] += 1
            
            # Update average processing time
            total_time = (self.performance_stats['average_processing_time'] * 
                         (self.performance_stats['total_recognitions'] - 1) + 
                         result['processing_time'])
            self.performance_stats['average_processing_time'] = total_time / self.performance_stats['total_recognitions']
            
            # Clean up temp file
            if temp_image_path and os.path.exists(temp_image_path):
                os.remove(temp_image_path)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Face recognition failed: {e}")
            return {
                'recognized': False,
                'student_info': None,
                'confidence': 0.0,
                'distance': None,
                'model_name': self.model_name,
                'processing_time': time.time() - start_time,
                'error': str(e),
                'analysis': None
            }
    
    def recognize_faces_in_frame(self, frame):
        """Recognize all faces in a video frame"""
        try:
            # Detect faces first
            face_locations = self._detect_faces(frame)
            
            if not face_locations:
                return frame, []
            
            recognition_results = []
            
            for i, (x, y, w, h) in enumerate(face_locations):
                # Extract face region
                face_region = frame[y:y+h, x:x+w]
                
                # Recognize face
                result = self.recognize_face(face_region, return_analysis=False)
                
                # Add location information
                result['face_location'] = (x, y, w, h)
                recognition_results.append(result)
                
                # Draw rectangle and label on frame
                color = (0, 255, 0) if result['recognized'] else (0, 0, 255)
                cv2.rectangle(frame, (x, y), (x+w, y+h), color, 2)
                
                # Add label
                if result['recognized']:
                    label = f"{result['student_info']['full_name']} ({result['confidence']:.1f}%)"
                else:
                    label = "Unknown"
                
                cv2.putText(frame, label, (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
            
            return frame, recognition_results
            
        except Exception as e:
            self.logger.error(f"Frame recognition failed: {e}")
            return frame, []
    
    def _detect_faces(self, frame):
        """Detect faces in frame using OpenCV"""
        try:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # Load face cascade
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            
            # Detect faces
            faces = face_cascade.detectMultiScale(gray, 1.1, 4)
            
            return faces
            
        except Exception as e:
            self.logger.error(f"Face detection failed: {e}")
            return []
    
    def _get_image_hash(self, image_path):
        """Get hash of image for caching"""
        try:
            import hashlib
            with open(image_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except Exception:
            return str(time.time())  # Fallback to timestamp
    
    def get_available_models(self):
        """Get list of available DeepFace models"""
        return ['VGG-Face', 'Facenet', 'Facenet512', 'OpenFace', 'DeepFace', 'DeepID', 'ArcFace', 'Dlib', 'SFace']
    
    def get_available_detectors(self):
        """Get list of available face detectors"""
        return ['opencv', 'ssd', 'dlib', 'mtcnn', 'retinaface', 'mediapipe']
    
    def change_model(self, model_name, detector_backend=None):
        """Change the recognition model"""
        try:
            if model_name in self.get_available_models():
                self.model_name = model_name
                if detector_backend:
                    self.detector_backend = detector_backend
                
                # Clear cache when changing models
                self.recognition_cache.clear()
                
                # Re-initialize
                self._initialize_models()
                
                self.logger.info(f"Changed to model: {model_name}")
                return True
            else:
                self.logger.error(f"Invalid model name: {model_name}")
                return False
                
        except Exception as e:
            self.logger.error(f"Model change failed: {e}")
            return False
    
    def get_performance_stats(self):
        """Get performance statistics"""
        stats = self.performance_stats.copy()
        stats['cache_size'] = len(self.recognition_cache)
        stats['known_faces_count'] = len(self.known_faces_cache)
        stats['model_name'] = self.model_name
        stats['detector_backend'] = self.detector_backend
        stats['distance_metric'] = self.distance_metric
        
        if stats['total_recognitions'] > 0:
            stats['success_rate'] = (stats['successful_recognitions'] / stats['total_recognitions']) * 100
        else:
            stats['success_rate'] = 0.0
        
        return stats
    
    def clear_cache(self):
        """Clear recognition cache"""
        self.recognition_cache.clear()
        self.logger.info("Recognition cache cleared")
    
    def reload_face_database(self):
        """Reload face database"""
        self._load_face_database()
        self.clear_cache()
        self.logger.info("Face database reloaded")
    
    def benchmark_models(self, test_image_path):
        """Benchmark different models on a test image"""
        try:
            results = {}
            models = self.get_available_models()
            
            for model in models:
                try:
                    start_time = time.time()
                    
                    # Temporarily change model
                    original_model = self.model_name
                    self.model_name = model
                    
                    # Test recognition
                    result = self.recognize_face(test_image_path, return_analysis=False)
                    
                    processing_time = time.time() - start_time
                    
                    results[model] = {
                        'processing_time': processing_time,
                        'recognized': result['recognized'],
                        'confidence': result['confidence'],
                        'success': True
                    }
                    
                    # Restore original model
                    self.model_name = original_model
                    
                except Exception as e:
                    results[model] = {
                        'processing_time': 0,
                        'recognized': False,
                        'confidence': 0,
                        'success': False,
                        'error': str(e)
                    }
            
            return results
            
        except Exception as e:
            self.logger.error(f"Model benchmarking failed: {e}")
            return {}
    
    def export_face_database(self, export_path):
        """Export face database for backup"""
        try:
            import shutil
            
            # Create export directory
            export_dir = Path(export_path)
            export_dir.mkdir(exist_ok=True)
            
            # Copy face database
            shutil.copytree(self.face_database_path, export_dir / "deepface_db", dirs_exist_ok=True)
            
            # Export metadata
            metadata = {
                'export_date': datetime.now().isoformat(),
                'model_name': self.model_name,
                'detector_backend': self.detector_backend,
                'distance_metric': self.distance_metric,
                'face_count': len(self.known_faces_cache)
            }
            
            import json
            with open(export_dir / "metadata.json", 'w') as f:
                json.dump(metadata, f, indent=2)
            
            self.logger.info(f"Face database exported to {export_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Face database export failed: {e}")
            return False
