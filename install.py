"""
Installation Script for Face Recognition Attendance System
Automates the setup process and dependency installation
"""

import subprocess
import sys
import os
from pathlib import Path

def print_header():
    """Print installation header"""
    print("=" * 60)
    print("Face Recognition Attendance System - Installation")
    print("=" * 60)
    print()

def check_python_version():
    """Check if Python version is compatible"""
    print("Checking Python version...")
    
    version = sys.version_info
    if version.major == 3 and version.minor >= 7:
        print(f"✓ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print(f"✗ Python {version.major}.{version.minor}.{version.micro} is not compatible")
        print("  Please install Python 3.7 or higher")
        return False

def install_package(package_name, pip_name=None):
    """Install a Python package"""
    if pip_name is None:
        pip_name = package_name
    
    try:
        print(f"Installing {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", pip_name])
        print(f"✓ {package_name} installed successfully")
        return True
    except subprocess.CalledProcessError:
        print(f"✗ Failed to install {package_name}")
        return False

def install_dependencies():
    """Install required dependencies"""
    print("\nInstalling dependencies...")

    # Update pip first
    print("Updating pip...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        print("✓ pip updated successfully")
    except subprocess.CalledProcessError:
        print("⚠️  pip update failed, continuing...")

    # Basic packages
    packages = [
        ("NumPy", "numpy==1.24.3"),
        ("OpenCV", "opencv-python==********"),
        ("Pillow", "Pillow==10.0.0"),
        ("Pandas", "pandas==2.0.3"),
        ("imutils", "imutils==0.5.4"),
        ("psutil", "psutil"),
        ("ttkthemes", "ttkthemes"),
        ("scikit-image", "scikit-image"),
        ("matplotlib", "matplotlib"),
        ("scikit-learn", "scikit-learn"),
        ("reportlab", "reportlab"),
        ("openpyxl", "openpyxl"),
        ("colorlog", "colorlog"),
        ("python-dateutil", "python-dateutil"),
        ("requests", "requests"),
        ("jsonschema", "jsonschema")
    ]

    failed_packages = []

    for package_name, pip_name in packages:
        if not install_package(package_name, pip_name):
            failed_packages.append(package_name)

    # Special handling for CMake (needed for dlib)
    print("\nInstalling build tools...")
    if not install_package("CMake", "cmake"):
        print("⚠️  CMake installation failed. This may cause issues with dlib.")
        failed_packages.append("CMake")

    # Special handling for dlib and face_recognition
    print("\nInstalling face recognition packages...")

    # Try to install dlib with different methods
    dlib_installed = False

    # Method 1: Try with specific version
    if install_package("dlib", "dlib==19.24.2"):
        dlib_installed = True
    # Method 2: Try latest version
    elif install_package("dlib", "dlib"):
        dlib_installed = True
    # Method 3: Try with conda if available
    else:
        print("⚠️  dlib installation failed with pip. Trying conda...")
        try:
            subprocess.check_call(["conda", "install", "-c", "conda-forge", "dlib", "-y"])
            print("✓ dlib installed with conda")
            dlib_installed = True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("✗ dlib installation failed with conda as well")
            failed_packages.append("dlib")

    # Try to install face_recognition
    if dlib_installed:
        if not install_package("face_recognition", "face-recognition==1.3.0"):
            print("⚠️  face_recognition installation failed. Trying alternative method...")
            if not install_package("face_recognition", "face-recognition"):
                failed_packages.append("face_recognition")
    else:
        print("⚠️  Skipping face_recognition installation due to dlib failure")
        failed_packages.append("face_recognition")

    return failed_packages

def create_directories():
    """Create required directories"""
    print("\nCreating directories...")
    
    directories = [
        "database",
        "faces", 
        "attendance"
    ]
    
    for directory in directories:
        dir_path = Path(directory)
        if not dir_path.exists():
            dir_path.mkdir(exist_ok=True)
            print(f"✓ Created {directory}/ directory")
        else:
            print(f"✓ {directory}/ directory already exists")

def setup_database():
    """Initialize the database"""
    print("\nSetting up database...")
    
    try:
        # Add current directory to Python path
        sys.path.append(str(Path(__file__).parent))
        
        from utils.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        print("✓ Database initialized successfully")
        
        # Test default admin user
        admin_user = db_manager.verify_admin_login("admin", "admin123")
        if admin_user:
            print("✓ Default admin user created")
        else:
            print("⚠️  Default admin user not found")
        
        db_manager.close_connection()
        return True
        
    except Exception as e:
        print(f"✗ Database setup failed: {e}")
        return False

def test_camera():
    """Test camera functionality"""
    print("\nTesting camera...")
    
    try:
        import cv2
        
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret:
                print("✓ Camera test successful")
                print(f"✓ Camera resolution: {frame.shape[1]}x{frame.shape[0]}")
            else:
                print("⚠️  Camera opened but failed to capture frame")
            cap.release()
            return True
        else:
            print("⚠️  Camera not accessible")
            return False
            
    except Exception as e:
        print(f"⚠️  Camera test failed: {e}")
        return False

def create_desktop_shortcut():
    """Create desktop shortcut (Windows only)"""
    if sys.platform == "win32":
        print("\nCreating desktop shortcut...")
        
        try:
            import winshell
            from win32com.client import Dispatch
            
            desktop = winshell.desktop()
            path = os.path.join(desktop, "Face Recognition Attendance.lnk")
            target = os.path.join(os.getcwd(), "main.py")
            wDir = os.getcwd()
            icon = target
            
            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(path)
            shortcut.Targetpath = sys.executable
            shortcut.Arguments = f'"{target}"'
            shortcut.WorkingDirectory = wDir
            shortcut.IconLocation = icon
            shortcut.save()
            
            print("✓ Desktop shortcut created")
            
        except ImportError:
            print("⚠️  Desktop shortcut creation requires pywin32")
        except Exception as e:
            print(f"⚠️  Desktop shortcut creation failed: {e}")

def main():
    """Main installation function"""
    print_header()
    
    # Check Python version
    if not check_python_version():
        input("Press Enter to exit...")
        return
    
    # Install dependencies
    failed_packages = install_dependencies()
    
    if failed_packages:
        print(f"\n⚠️  The following packages failed to install: {', '.join(failed_packages)}")
        print("You may need to install them manually or check your system configuration.")
        
        if "dlib" in failed_packages or "face_recognition" in failed_packages:
            print("\nFor face_recognition installation issues:")
            print("- Windows: Install Visual Studio Build Tools")
            print("- Linux: sudo apt-get install cmake libopenblas-dev liblapack-dev")
            print("- macOS: brew install cmake")
    
    # Create directories
    create_directories()
    
    # Setup database
    if not setup_database():
        print("⚠️  Database setup failed. You may need to run this manually.")
    
    # Test camera
    camera_ok = test_camera()
    
    # Create desktop shortcut (Windows only)
    create_desktop_shortcut()
    
    # Final summary
    print("\n" + "=" * 60)
    print("Installation Summary")
    print("=" * 60)
    
    if not failed_packages:
        print("✓ All dependencies installed successfully")
    else:
        print(f"⚠️  {len(failed_packages)} packages failed to install")
    
    print("✓ Directories created")
    print("✓ Database initialized" if setup_database else "⚠️  Database setup issues")
    print("✓ Camera working" if camera_ok else "⚠️  Camera issues detected")
    
    print("\n🎉 Installation completed!")
    print("\nNext steps:")
    print("1. Run the test script: python test_system.py")
    print("2. Start the application: python main.py")
    print("3. Login with username: admin, password: admin123")
    
    if failed_packages or not camera_ok:
        print("\n⚠️  Please resolve the issues mentioned above before using the system.")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
