"""
Attendance Recognition Window for Face Recognition Attendance System
Handles real-time face recognition and attendance marking
"""

import tkinter as tk
from tkinter import ttk, messagebox
import cv2
from PIL import Image, ImageTk
import threading
import time
from datetime import datetime, date
import config
from utils.database_manager import DatabaseManager
from utils.face_recognizer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from utils.helper import <PERSON><PERSON>anager, UIHelper, ImageProcessor, DateTimeHelper
from utils.logger import setup_logger, log_system_event, log_attendance_event

class AttendanceWindow:
    """Attendance window with real-time face recognition"""
    
    def __init__(self, parent):
        self.parent = parent
        self.db_manager = DatabaseManager()
        self.face_recognizer = FaceRecognizer()
        self.camera_manager = CameraManager()
        self.logger = setup_logger("AttendanceWindow")
        
        # Attendance state
        self.is_camera_active = False
        self.is_recognition_active = False
        self.camera_thread = None
        self.attendance_session_active = False
        
        # Statistics
        self.session_stats = {
            'total_recognized': 0,
            'attendance_marked': 0,
            'unknown_faces': 0,
            'session_start': None
        }
        
        # Create attendance window
        self.window = tk.Toplevel(parent)
        self.window.title("Face Recognition Attendance")
        self.window.geometry("1000x750")
        self.window.configure(bg=config.BACKGROUND_COLOR)
        
        # Center window
        UIHelper.center_window(self.window, 1000, 750)
        
        # Make window modal
        self.window.transient(parent)
        self.window.grab_set()
        
        # Setup UI
        self.setup_ui()
        
        # Handle window close
        self.window.protocol("WM_DELETE_WINDOW", self.on_close)
        
        # Load initial data
        self.refresh_attendance_list()
    
    def setup_ui(self):
        """Setup the attendance UI"""
        # Main container
        main_frame = tk.Frame(self.window, bg=config.BACKGROUND_COLOR)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Title
        title_label = tk.Label(
            main_frame,
            text="Face Recognition Attendance",
            font=('Arial', 18, 'bold'),
            fg=config.THEME_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        title_label.pack(pady=(0, 20))
        
        # Create layout
        content_frame = tk.Frame(main_frame, bg=config.BACKGROUND_COLOR)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # Left panel - Camera and controls
        self.create_camera_panel(content_frame)
        
        # Right panel - Attendance list and stats
        self.create_attendance_panel(content_frame)
        
        # Bottom controls
        self.create_control_panel(main_frame)
    
    def create_camera_panel(self, parent):
        """Create camera panel"""
        # Left frame
        left_frame = tk.Frame(parent, bg=config.BACKGROUND_COLOR)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # Camera title
        camera_title = tk.Label(
            left_frame,
            text="Live Camera Feed",
            font=('Arial', 14, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        camera_title.pack(anchor=tk.W, pady=(0, 10))
        
        # Camera frame
        self.camera_frame = tk.Frame(
            left_frame,
            bg='black',
            width=500,
            height=375,
            relief=tk.SUNKEN,
            bd=2
        )
        self.camera_frame.pack(pady=(0, 10))
        self.camera_frame.pack_propagate(False)
        
        # Camera display label
        self.camera_label = tk.Label(
            self.camera_frame,
            text="Camera Preview\nClick 'Start Camera' to begin",
            font=('Arial', 14),
            fg='white',
            bg='black',
            justify=tk.CENTER
        )
        self.camera_label.pack(expand=True)
        
        # Camera controls
        camera_controls = tk.Frame(left_frame, bg=config.BACKGROUND_COLOR)
        camera_controls.pack(fill=tk.X, pady=(0, 10))
        
        self.start_camera_btn = tk.Button(
            camera_controls,
            text="Start Camera",
            font=('Arial', 11, 'bold'),
            bg=config.BUTTON_COLOR,
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.toggle_camera
        )
        self.start_camera_btn.pack(side=tk.LEFT, padx=(0, 5), ipady=5, ipadx=10)
        
        self.recognition_btn = tk.Button(
            camera_controls,
            text="Start Recognition",
            font=('Arial', 11, 'bold'),
            bg='green',
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            state=tk.DISABLED,
            command=self.toggle_recognition
        )
        self.recognition_btn.pack(side=tk.LEFT, padx=5, ipady=5, ipadx=10)
        
        # Recognition status
        self.recognition_status_label = tk.Label(
            left_frame,
            text="Recognition: Inactive",
            font=('Arial', 12, 'bold'),
            fg='red',
            bg=config.BACKGROUND_COLOR
        )
        self.recognition_status_label.pack(pady=(10, 0))
        
        # Current recognition info
        self.current_recognition_label = tk.Label(
            left_frame,
            text="",
            font=('Arial', 11),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR,
            wraplength=450,
            justify=tk.LEFT
        )
        self.current_recognition_label.pack(pady=(5, 0))
    
    def create_attendance_panel(self, parent):
        """Create attendance panel"""
        # Right frame
        right_frame = tk.Frame(parent, bg=config.BACKGROUND_COLOR)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))
        
        # Attendance title
        attendance_title = tk.Label(
            right_frame,
            text="Today's Attendance",
            font=('Arial', 14, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        attendance_title.pack(anchor=tk.W, pady=(0, 10))
        
        # Date display
        today_date = DateTimeHelper.get_current_date_string()
        date_label = tk.Label(
            right_frame,
            text=f"Date: {today_date}",
            font=('Arial', 11),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        date_label.pack(anchor=tk.W, pady=(0, 10))
        
        # Attendance list frame
        list_frame = tk.Frame(right_frame, bg=config.BACKGROUND_COLOR)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Attendance treeview
        columns = ('Time', 'Roll No', 'Name', 'Class', 'Confidence')
        self.attendance_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=12)
        
        # Configure columns
        self.attendance_tree.heading('Time', text='Time')
        self.attendance_tree.heading('Roll No', text='Roll No')
        self.attendance_tree.heading('Name', text='Name')
        self.attendance_tree.heading('Class', text='Class')
        self.attendance_tree.heading('Confidence', text='Confidence')
        
        self.attendance_tree.column('Time', width=80)
        self.attendance_tree.column('Roll No', width=80)
        self.attendance_tree.column('Name', width=120)
        self.attendance_tree.column('Class', width=80)
        self.attendance_tree.column('Confidence', width=80)
        
        # Scrollbar for treeview
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.attendance_tree.yview)
        self.attendance_tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack treeview and scrollbar
        self.attendance_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Statistics frame
        stats_frame = tk.LabelFrame(
            right_frame,
            text="Session Statistics",
            font=('Arial', 11, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        stats_frame.pack(fill=tk.X, pady=(10, 0))
        
        # Statistics labels
        self.stats_labels = {}
        stats_info = [
            ('session_start', 'Session Start: Not started'),
            ('total_recognized', 'Total Recognized: 0'),
            ('attendance_marked', 'Attendance Marked: 0'),
            ('unknown_faces', 'Unknown Faces: 0')
        ]
        
        for key, text in stats_info:
            label = tk.Label(
                stats_frame,
                text=text,
                font=('Arial', 10),
                fg=config.TEXT_COLOR,
                bg=config.BACKGROUND_COLOR
            )
            label.pack(anchor=tk.W, padx=10, pady=2)
            self.stats_labels[key] = label
    
    def create_control_panel(self, parent):
        """Create control panel"""
        control_frame = tk.Frame(parent, bg=config.BACKGROUND_COLOR)
        control_frame.pack(fill=tk.X, pady=(20, 0))
        
        # Session controls
        session_frame = tk.LabelFrame(
            control_frame,
            text="Session Controls",
            font=('Arial', 11, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        session_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        session_buttons = tk.Frame(session_frame, bg=config.BACKGROUND_COLOR)
        session_buttons.pack(fill=tk.X, padx=10, pady=10)
        
        self.start_session_btn = tk.Button(
            session_buttons,
            text="Start Session",
            font=('Arial', 11, 'bold'),
            bg=config.THEME_COLOR,
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.start_attendance_session
        )
        self.start_session_btn.pack(side=tk.LEFT, padx=(0, 5), ipady=5, ipadx=15)
        
        self.end_session_btn = tk.Button(
            session_buttons,
            text="End Session",
            font=('Arial', 11, 'bold'),
            bg='red',
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            state=tk.DISABLED,
            command=self.end_attendance_session
        )
        self.end_session_btn.pack(side=tk.LEFT, padx=5, ipady=5, ipadx=15)
        
        # Action buttons
        action_frame = tk.LabelFrame(
            control_frame,
            text="Actions",
            font=('Arial', 11, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        action_frame.pack(side=tk.RIGHT, padx=(10, 0))
        
        action_buttons = tk.Frame(action_frame, bg=config.BACKGROUND_COLOR)
        action_buttons.pack(fill=tk.X, padx=10, pady=10)
        
        refresh_btn = tk.Button(
            action_buttons,
            text="Refresh",
            font=('Arial', 11),
            bg='orange',
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.refresh_attendance_list
        )
        refresh_btn.pack(side=tk.LEFT, padx=(0, 5), ipady=5, ipadx=15)
        
        export_btn = tk.Button(
            action_buttons,
            text="Export",
            font=('Arial', 11),
            bg='purple',
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.export_attendance
        )
        export_btn.pack(side=tk.LEFT, padx=5, ipady=5, ipadx=15)
        
        close_btn = tk.Button(
            action_buttons,
            text="Close",
            font=('Arial', 11),
            bg='gray',
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.on_close
        )
        close_btn.pack(side=tk.LEFT, padx=(5, 0), ipady=5, ipadx=15)

    def toggle_camera(self):
        """Toggle camera on/off"""
        if not self.is_camera_active:
            self.start_camera()
        else:
            self.stop_camera()

    def start_camera(self):
        """Start camera preview"""
        try:
            if self.camera_manager.initialize_camera():
                self.is_camera_active = True
                self.start_camera_btn.config(text="Stop Camera", bg='red')
                self.recognition_btn.config(state=tk.NORMAL)

                # Start camera thread
                self.camera_thread = threading.Thread(target=self.camera_loop, daemon=True)
                self.camera_thread.start()

                log_system_event("CAMERA", "Camera started for attendance")
            else:
                UIHelper.show_error_message("Camera Error", "Failed to initialize camera")

        except Exception as e:
            self.logger.error(f"Camera start error: {str(e)}")
            UIHelper.show_error_message("Camera Error", f"Camera error: {str(e)}")

    def stop_camera(self):
        """Stop camera preview"""
        # Stop recognition first
        if self.is_recognition_active:
            self.stop_recognition()

        self.is_camera_active = False
        self.camera_manager.release_camera()
        self.start_camera_btn.config(text="Start Camera", bg=config.BUTTON_COLOR)
        self.recognition_btn.config(state=tk.DISABLED)

        # Reset camera display
        self.camera_label.config(
            image='',
            text="Camera Preview\nClick 'Start Camera' to begin"
        )

        log_system_event("CAMERA", "Camera stopped")

    def toggle_recognition(self):
        """Toggle face recognition on/off"""
        if not self.is_recognition_active:
            self.start_recognition()
        else:
            self.stop_recognition()

    def start_recognition(self):
        """Start face recognition"""
        if not self.attendance_session_active:
            if not UIHelper.ask_yes_no(
                "Start Session",
                "No attendance session is active. Start a new session?"
            ):
                return
            self.start_attendance_session()

        self.is_recognition_active = True
        self.recognition_btn.config(text="Stop Recognition", bg='red')
        self.recognition_status_label.config(text="Recognition: Active", fg='green')

        log_system_event("RECOGNITION", "Face recognition started")

    def stop_recognition(self):
        """Stop face recognition"""
        self.is_recognition_active = False
        self.recognition_btn.config(text="Start Recognition", bg='green')
        self.recognition_status_label.config(text="Recognition: Inactive", fg='red')
        self.current_recognition_label.config(text="")

        log_system_event("RECOGNITION", "Face recognition stopped")

    def camera_loop(self):
        """Camera preview and recognition loop"""
        while self.is_camera_active:
            try:
                frame = self.camera_manager.read_frame()
                if frame is not None:
                    # Process frame for recognition if active
                    if self.is_recognition_active:
                        processed_frame, attendance_results = self.face_recognizer.process_frame_for_attendance(frame)

                        # Update recognition info
                        self.update_recognition_info(attendance_results)

                        # Update statistics
                        self.update_session_stats(attendance_results)

                        display_frame = processed_frame
                    else:
                        display_frame = frame

                    # Resize frame for display
                    display_frame = ImageProcessor.resize_image_for_display(display_frame, 500, 375)

                    # Convert to PhotoImage
                    photo = ImageProcessor.convert_cv2_to_tkinter(display_frame)

                    # Update display
                    self.camera_label.config(image=photo, text='')
                    self.camera_label.image = photo  # Keep reference

                time.sleep(0.033)  # ~30 FPS

            except Exception as e:
                self.logger.error(f"Camera loop error: {str(e)}")
                break

    def update_recognition_info(self, attendance_results):
        """Update recognition information display"""
        if not attendance_results:
            self.current_recognition_label.config(text="No faces detected")
            return

        info_lines = []
        for result in attendance_results:
            if result['student_info']:
                student = result['student_info']
                confidence = result['confidence']
                status = "✓ Marked" if result['attendance_marked'] else "Already marked"
                info_lines.append(f"{student['full_name']} ({confidence:.1f}%) - {status}")
            else:
                info_lines.append("Unknown face detected")

        self.current_recognition_label.config(text="\n".join(info_lines))

    def update_session_stats(self, attendance_results):
        """Update session statistics"""
        for result in attendance_results:
            if result['student_info']:
                self.session_stats['total_recognized'] += 1
                if result['attendance_marked']:
                    self.session_stats['attendance_marked'] += 1
                    # Refresh attendance list when new attendance is marked
                    self.window.after_idle(self.refresh_attendance_list)
            else:
                self.session_stats['unknown_faces'] += 1

        # Update statistics display
        self.update_stats_display()

    def update_stats_display(self):
        """Update statistics display"""
        stats = self.session_stats

        session_start_text = "Not started"
        if stats['session_start']:
            session_start_text = stats['session_start'].strftime("%H:%M:%S")

        self.stats_labels['session_start'].config(text=f"Session Start: {session_start_text}")
        self.stats_labels['total_recognized'].config(text=f"Total Recognized: {stats['total_recognized']}")
        self.stats_labels['attendance_marked'].config(text=f"Attendance Marked: {stats['attendance_marked']}")
        self.stats_labels['unknown_faces'].config(text=f"Unknown Faces: {stats['unknown_faces']}")

    def start_attendance_session(self):
        """Start attendance session"""
        self.attendance_session_active = True
        self.session_stats['session_start'] = datetime.now()

        self.start_session_btn.config(state=tk.DISABLED)
        self.end_session_btn.config(state=tk.NORMAL)

        self.update_stats_display()

        log_system_event("SESSION", "Attendance session started")
        UIHelper.show_info_message("Session Started", "Attendance session has been started!")

    def end_attendance_session(self):
        """End attendance session"""
        if UIHelper.ask_yes_no("End Session", "Are you sure you want to end the attendance session?"):
            # Stop recognition if active
            if self.is_recognition_active:
                self.stop_recognition()

            self.attendance_session_active = False

            self.start_session_btn.config(state=tk.NORMAL)
            self.end_session_btn.config(state=tk.DISABLED)

            # Show session summary
            stats = self.session_stats
            session_duration = datetime.now() - stats['session_start']

            summary = f"""Session Summary:
Duration: {str(session_duration).split('.')[0]}
Total Recognized: {stats['total_recognized']}
Attendance Marked: {stats['attendance_marked']}
Unknown Faces: {stats['unknown_faces']}"""

            UIHelper.show_info_message("Session Ended", summary)

            log_system_event("SESSION", f"Attendance session ended - {stats['attendance_marked']} attendance marked")

    def refresh_attendance_list(self):
        """Refresh attendance list"""
        try:
            # Clear existing items
            for item in self.attendance_tree.get_children():
                self.attendance_tree.delete(item)

            # Get today's attendance
            today = date.today()
            attendance_records = self.db_manager.get_attendance_by_date(today)

            # Populate treeview
            for record in attendance_records:
                time_str = datetime.fromisoformat(record['attendance_time']).strftime("%H:%M:%S")
                confidence_str = f"{record['confidence_score']:.1f}%" if record['confidence_score'] else "N/A"

                self.attendance_tree.insert('', tk.END, values=(
                    time_str,
                    record['roll_number'],
                    record['full_name'],
                    record['class_name'],
                    confidence_str
                ))

        except Exception as e:
            self.logger.error(f"Failed to refresh attendance list: {str(e)}")

    def export_attendance(self):
        """Export today's attendance"""
        try:
            from utils.helper import ReportGenerator

            report_generator = ReportGenerator(self.db_manager)
            today = date.today()

            # Generate report
            df = report_generator.generate_daily_attendance_report(today)

            if df.empty:
                UIHelper.show_info_message("No Data", "No attendance records found for today.")
                return

            # Save file
            filename = f"attendance_{today.strftime('%Y%m%d')}.csv"
            file_path = report_generator.export_report_to_csv(df, filename)

            if file_path:
                UIHelper.show_info_message(
                    "Export Successful",
                    f"Attendance exported successfully!\nFile: {file_path}"
                )
            else:
                UIHelper.show_error_message("Export Failed", "Failed to export attendance data.")

        except Exception as e:
            self.logger.error(f"Export error: {str(e)}")
            UIHelper.show_error_message("Export Error", f"Export failed: {str(e)}")

    def on_close(self):
        """Handle window close"""
        # Stop camera if active
        if self.is_camera_active:
            self.stop_camera()

        # End session if active
        if self.attendance_session_active:
            if UIHelper.ask_yes_no("End Session", "End the current attendance session?"):
                self.end_attendance_session()
            else:
                return  # Don't close if user cancels

        # Close window
        self.window.destroy()

        log_system_event("WINDOW_CLOSE", "Attendance window closed")
