"""
Login Window for Face Recognition Attendance System
Handles admin authentication
"""

import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk
import config
from utils.database_manager import DatabaseManager
from utils.logger import log_system_event, setup_logger
from utils.helper import UIHelper

class LoginWindow:
    """Login window for admin authentication"""
    
    def __init__(self, parent, login_callback):
        self.parent = parent
        self.login_callback = login_callback
        self.db_manager = DatabaseManager()
        self.logger = setup_logger("LoginWindow")
        
        # Create login window
        self.window = tk.Toplevel(parent)
        self.window.title("Face Recognition Attendance System - Login")
        self.window.geometry("400x500")
        self.window.configure(bg=config.BACKGROUND_COLOR)
        self.window.resizable(False, False)
        
        # Center window
        UIHelper.center_window(self.window, 400, 500)
        
        # Make window modal
        self.window.transient(parent)
        self.window.grab_set()
        
        # Setup UI
        self.setup_ui()
        
        # Focus on username entry
        self.username_entry.focus()
        
        # Bind Enter key to login
        self.window.bind('<Return>', lambda event: self.login())
        
        # Handle window close
        self.window.protocol("WM_DELETE_WINDOW", self.on_close)
    
    def setup_ui(self):
        """Setup the login UI"""
        # Main frame
        main_frame = tk.Frame(self.window, bg=config.BACKGROUND_COLOR)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Title frame
        title_frame = tk.Frame(main_frame, bg=config.BACKGROUND_COLOR)
        title_frame.pack(fill=tk.X, pady=(0, 30))
        
        # System title
        title_label = tk.Label(
            title_frame,
            text="Face Recognition\nAttendance System",
            font=('Arial', 18, 'bold'),
            fg=config.THEME_COLOR,
            bg=config.BACKGROUND_COLOR,
            justify=tk.CENTER
        )
        title_label.pack()
        
        # Subtitle
        subtitle_label = tk.Label(
            title_frame,
            text="Admin Login",
            font=('Arial', 12),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        subtitle_label.pack(pady=(10, 0))
        
        # Login form frame
        form_frame = tk.Frame(main_frame, bg=config.BACKGROUND_COLOR)
        form_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Username field
        username_label = tk.Label(
            form_frame,
            text="Username:",
            font=('Arial', 11, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        username_label.pack(anchor=tk.W, pady=(0, 5))
        
        self.username_entry = tk.Entry(
            form_frame,
            font=('Arial', 11),
            relief=tk.FLAT,
            bd=1,
            highlightthickness=2,
            highlightcolor=config.THEME_COLOR
        )
        self.username_entry.pack(fill=tk.X, ipady=8, pady=(0, 15))
        
        # Password field
        password_label = tk.Label(
            form_frame,
            text="Password:",
            font=('Arial', 11, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        password_label.pack(anchor=tk.W, pady=(0, 5))
        
        self.password_entry = tk.Entry(
            form_frame,
            font=('Arial', 11),
            show="*",
            relief=tk.FLAT,
            bd=1,
            highlightthickness=2,
            highlightcolor=config.THEME_COLOR
        )
        self.password_entry.pack(fill=tk.X, ipady=8, pady=(0, 20))
        
        # Login button
        self.login_button = tk.Button(
            form_frame,
            text="Login",
            font=('Arial', 12, 'bold'),
            bg=config.BUTTON_COLOR,
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.login
        )
        self.login_button.pack(fill=tk.X, ipady=10, pady=(0, 10))
        
        # Status label
        self.status_label = tk.Label(
            form_frame,
            text="",
            font=('Arial', 10),
            fg='red',
            bg=config.BACKGROUND_COLOR
        )
        self.status_label.pack(pady=(10, 0))
        
        # Info frame
        info_frame = tk.Frame(main_frame, bg=config.BACKGROUND_COLOR)
        info_frame.pack(fill=tk.X, side=tk.BOTTOM)
        
        # Default credentials info
        info_text = "Default Login:\nUsername: admin\nPassword: admin123"
        info_label = tk.Label(
            info_frame,
            text=info_text,
            font=('Arial', 9),
            fg='gray',
            bg=config.BACKGROUND_COLOR,
            justify=tk.CENTER
        )
        info_label.pack(pady=(20, 0))
        
        # Version info
        version_label = tk.Label(
            info_frame,
            text="Version 1.0",
            font=('Arial', 8),
            fg='lightgray',
            bg=config.BACKGROUND_COLOR
        )
        version_label.pack(side=tk.BOTTOM, pady=(10, 0))
    
    def login(self):
        """Handle login attempt"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        
        # Clear status
        self.status_label.config(text="")
        
        # Validate input
        if not username:
            self.show_error("Please enter username")
            self.username_entry.focus()
            return
        
        if not password:
            self.show_error("Please enter password")
            self.password_entry.focus()
            return
        
        # Disable login button during authentication
        self.login_button.config(state=tk.DISABLED, text="Logging in...")
        self.window.update()
        
        try:
            # Verify credentials
            user_data = self.db_manager.verify_admin_login(username, password)
            
            if user_data:
                # Login successful
                log_system_event("LOGIN", f"User {username} logged in successfully")
                
                # Close login window
                self.window.destroy()
                
                # Call login callback
                self.login_callback(user_data)
                
            else:
                # Login failed
                log_system_event("LOGIN_FAILED", f"Failed login attempt for user {username}")
                self.show_error("Invalid username or password")
                self.password_entry.delete(0, tk.END)
                self.password_entry.focus()
                
        except Exception as e:
            self.logger.error(f"Login error: {str(e)}")
            self.show_error("Login error occurred. Please try again.")
            
        finally:
            # Re-enable login button
            self.login_button.config(state=tk.NORMAL, text="Login")
    
    def show_error(self, message):
        """Show error message"""
        self.status_label.config(text=message, fg='red')
    
    def show_success(self, message):
        """Show success message"""
        self.status_label.config(text=message, fg='green')
    
    def on_close(self):
        """Handle window close"""
        # Exit application if login window is closed
        self.parent.quit()

class ChangePasswordDialog:
    """Dialog for changing admin password"""
    
    def __init__(self, parent, user_data):
        self.parent = parent
        self.user_data = user_data
        self.db_manager = DatabaseManager()
        self.result = False
        
        # Create dialog window
        self.window = tk.Toplevel(parent)
        self.window.title("Change Password")
        self.window.geometry("350x300")
        self.window.configure(bg=config.BACKGROUND_COLOR)
        self.window.resizable(False, False)
        
        # Center window
        UIHelper.center_window(self.window, 350, 300)
        
        # Make window modal
        self.window.transient(parent)
        self.window.grab_set()
        
        # Setup UI
        self.setup_ui()
        
        # Focus on current password entry
        self.current_password_entry.focus()
    
    def setup_ui(self):
        """Setup the change password UI"""
        # Main frame
        main_frame = tk.Frame(self.window, bg=config.BACKGROUND_COLOR)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Title
        title_label = tk.Label(
            main_frame,
            text="Change Password",
            font=('Arial', 14, 'bold'),
            fg=config.THEME_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        title_label.pack(pady=(0, 20))
        
        # Current password field
        current_label = tk.Label(
            main_frame,
            text="Current Password:",
            font=('Arial', 10, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        current_label.pack(anchor=tk.W, pady=(0, 5))
        
        self.current_password_entry = tk.Entry(
            main_frame,
            font=('Arial', 10),
            show="*",
            relief=tk.FLAT,
            bd=1
        )
        self.current_password_entry.pack(fill=tk.X, ipady=5, pady=(0, 15))
        
        # New password field
        new_label = tk.Label(
            main_frame,
            text="New Password:",
            font=('Arial', 10, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        new_label.pack(anchor=tk.W, pady=(0, 5))
        
        self.new_password_entry = tk.Entry(
            main_frame,
            font=('Arial', 10),
            show="*",
            relief=tk.FLAT,
            bd=1
        )
        self.new_password_entry.pack(fill=tk.X, ipady=5, pady=(0, 15))
        
        # Confirm password field
        confirm_label = tk.Label(
            main_frame,
            text="Confirm New Password:",
            font=('Arial', 10, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        confirm_label.pack(anchor=tk.W, pady=(0, 5))
        
        self.confirm_password_entry = tk.Entry(
            main_frame,
            font=('Arial', 10),
            show="*",
            relief=tk.FLAT,
            bd=1
        )
        self.confirm_password_entry.pack(fill=tk.X, ipady=5, pady=(0, 20))
        
        # Buttons frame
        buttons_frame = tk.Frame(main_frame, bg=config.BACKGROUND_COLOR)
        buttons_frame.pack(fill=tk.X, pady=(10, 0))
        
        # Change password button
        change_button = tk.Button(
            buttons_frame,
            text="Change Password",
            font=('Arial', 10, 'bold'),
            bg=config.BUTTON_COLOR,
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.change_password
        )
        change_button.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        
        # Cancel button
        cancel_button = tk.Button(
            buttons_frame,
            text="Cancel",
            font=('Arial', 10),
            bg='gray',
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.window.destroy
        )
        cancel_button.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(5, 0))
        
        # Status label
        self.status_label = tk.Label(
            main_frame,
            text="",
            font=('Arial', 9),
            bg=config.BACKGROUND_COLOR
        )
        self.status_label.pack(pady=(10, 0))
    
    def change_password(self):
        """Handle password change"""
        current_password = self.current_password_entry.get()
        new_password = self.new_password_entry.get()
        confirm_password = self.confirm_password_entry.get()
        
        # Validate input
        if not current_password:
            self.show_error("Please enter current password")
            return
        
        if not new_password:
            self.show_error("Please enter new password")
            return
        
        if len(new_password) < config.PASSWORD_MIN_LENGTH:
            self.show_error(f"Password must be at least {config.PASSWORD_MIN_LENGTH} characters")
            return
        
        if new_password != confirm_password:
            self.show_error("New passwords do not match")
            return
        
        try:
            # Verify current password
            user_data = self.db_manager.verify_admin_login(
                self.user_data['username'], 
                current_password
            )
            
            if not user_data:
                self.show_error("Current password is incorrect")
                return
            
            # Update password (implementation needed in database_manager)
            # For now, show success message
            self.show_success("Password changed successfully!")
            self.result = True
            
            # Close dialog after 2 seconds
            self.window.after(2000, self.window.destroy)
            
        except Exception as e:
            self.show_error("Error changing password")
    
    def show_error(self, message):
        """Show error message"""
        self.status_label.config(text=message, fg='red')
    
    def show_success(self, message):
        """Show success message"""
        self.status_label.config(text=message, fg='green')
