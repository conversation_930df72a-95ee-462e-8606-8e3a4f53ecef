"""
Face Recognition Utility for Face Recognition Attendance System
Handles real-time face recognition and matching
Supports both face_recognition and DeepFace libraries
"""

import cv2
import numpy as np
from datetime import datetime, timedelta
import config
from utils.database_manager import DatabaseManager
from utils.logger import log_face_recognition_event, log_attendance_event, setup_logger

# Import based on configuration
if config.USE_DEEPFACE:
    try:
        from utils.deepface_recognizer import DeepFaceRecognizer
        DEEPFACE_AVAILABLE = True
        FACE_RECOGNITION_AVAILABLE = False
    except ImportError:
        DEEPFACE_AVAILABLE = False
        try:
            import face_recognition
            FACE_RECOGNITION_AVAILABLE = True
        except ImportError:
            FACE_RECOGNITION_AVAILABLE = False
else:
    try:
        import face_recognition
        FACE_RECOGNITION_AVAILABLE = True
        DEEPFACE_AVAILABLE = False
    except ImportError:
        FACE_RECOGNITION_AVAILABLE = False
        try:
            from utils.deepface_recognizer import DeepFaceRecognizer
            DEEPFACE_AVAILABLE = True
        except ImportError:
            DEEPFACE_AVAILABLE = False

class FaceRecognizer:
    """Handles face recognition operations with support for both face_recognition and DeepFace"""

    def __init__(self):
        self.logger = setup_logger("FaceRecognizer")
        self.db_manager = DatabaseManager()
        self.tolerance = config.TOLERANCE
        self.model = config.MODEL

        # Determine which library to use
        self.use_deepface = config.USE_DEEPFACE and DEEPFACE_AVAILABLE

        if self.use_deepface:
            # Initialize DeepFace recognizer
            self.deepface_recognizer = DeepFaceRecognizer(
                model_name=config.DEEPFACE_MODEL,
                detector_backend=config.DEEPFACE_DETECTOR,
                distance_metric=config.DEEPFACE_DISTANCE_METRIC
            )
            self.logger.info("Using DeepFace for face recognition")
        elif FACE_RECOGNITION_AVAILABLE:
            # Load known faces for face_recognition library
            self.known_face_encodings = []
            self.known_face_info = []
            self.load_known_faces()
            self.logger.info("Using face_recognition library")
        else:
            raise ImportError("Neither DeepFace nor face_recognition library is available")

        # Recognition cache to prevent duplicate attendance
        self.recognition_cache = {}
        self.cache_timeout = config.MIN_ATTENDANCE_INTERVAL
        
    def load_known_faces(self):
        """Load known face encodings from database"""
        try:
            if self.use_deepface:
                # DeepFace handles its own face database
                self.deepface_recognizer.reload_face_database()
                stats = self.deepface_recognizer.get_performance_stats()
                face_count = stats.get('known_faces_count', 0)
                self.logger.info(f"Loaded {face_count} known faces for DeepFace")
            else:
                # Load for face_recognition library
                encodings, student_info = self.db_manager.get_student_face_encodings()
                self.known_face_encodings = encodings
                self.known_face_info = student_info

                self.logger.info(f"Loaded {len(encodings)} known face encodings")

            log_face_recognition_event("LOAD_FACES", {
                "faces_loaded": face_count if self.use_deepface else len(encodings),
                "library": "DeepFace" if self.use_deepface else "face_recognition"
            })

        except Exception as e:
            self.logger.error(f"Failed to load known faces: {str(e)}")
            if not self.use_deepface:
                self.known_face_encodings = []
                self.known_face_info = []
    
    def reload_known_faces(self):
        """Reload known faces (call after adding new students)"""
        self.load_known_faces()
    
    def recognize_faces_in_frame(self, frame):
        """
        Recognize faces in a video frame

        Args:
            frame: OpenCV frame (BGR format)

        Returns:
            list: List of recognition results
        """
        try:
            if self.use_deepface:
                return self._recognize_faces_deepface(frame)
            else:
                return self._recognize_faces_face_recognition(frame)

        except Exception as e:
            self.logger.error(f"Face recognition failed: {str(e)}")
            return []

    def _recognize_faces_deepface(self, frame):
        """Recognize faces using DeepFace"""
        try:
            # Use DeepFace recognizer
            annotated_frame, results = self.deepface_recognizer.recognize_faces_in_frame(frame)

            # Convert DeepFace results to our format
            recognition_results = []
            for result in results:
                recognition_results.append({
                    'face_location': result.get('face_location', (0, 0, 0, 0)),
                    'student_info': result.get('student_info'),
                    'confidence': result.get('confidence', 0.0),
                    'face_encoding': result.get('face_encoding')
                })

            return recognition_results

        except Exception as e:
            self.logger.error(f"DeepFace recognition failed: {str(e)}")
            return []

    def _recognize_faces_face_recognition(self, frame):
        """Recognize faces using face_recognition library"""
        try:
            # Convert BGR to RGB
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # Find face locations and encodings
            face_locations = face_recognition.face_locations(rgb_frame, model=self.model)
            face_encodings = face_recognition.face_encodings(rgb_frame, face_locations)

            recognition_results = []

            for face_encoding, face_location in zip(face_encodings, face_locations):
                # Compare with known faces
                matches = face_recognition.compare_faces(
                    self.known_face_encodings,
                    face_encoding,
                    tolerance=self.tolerance
                )

                # Calculate face distances
                face_distances = face_recognition.face_distance(
                    self.known_face_encodings,
                    face_encoding
                )

                student_info = None
                confidence = 0.0

                if True in matches:
                    # Find best match
                    best_match_index = np.argmin(face_distances)

                    if matches[best_match_index]:
                        student_info = self.known_face_info[best_match_index]
                        # Convert distance to confidence percentage
                        confidence = max(0, (1 - face_distances[best_match_index]) * 100)

                recognition_results.append({
                    'face_location': face_location,
                    'student_info': student_info,
                    'confidence': confidence,
                    'face_encoding': face_encoding
                })

            return recognition_results

        except Exception as e:
            self.logger.error(f"face_recognition library recognition failed: {str(e)}")
            return []
    
    def mark_attendance_for_recognized_face(self, student_info, confidence):
        """
        Mark attendance for a recognized student
        
        Args:
            student_info: Student information dictionary
            confidence: Recognition confidence score
            
        Returns:
            tuple: (success, message)
        """
        try:
            student_id = student_info['id']
            student_name = student_info['full_name']
            roll_number = student_info['roll_number']
            
            # Check cache to prevent duplicate marking
            cache_key = f"{student_id}_{datetime.now().date()}"
            current_time = datetime.now()
            
            if cache_key in self.recognition_cache:
                last_recognition = self.recognition_cache[cache_key]
                time_diff = (current_time - last_recognition).total_seconds()
                
                if time_diff < self.cache_timeout:
                    return False, f"Attendance recently marked for {student_name}"
            
            # Mark attendance in database
            success, message = self.db_manager.mark_attendance(student_id, confidence)
            
            if success:
                # Update cache
                self.recognition_cache[cache_key] = current_time
                
                # Log attendance event
                log_attendance_event(student_name, roll_number, "Present", confidence)
                
                log_face_recognition_event("ATTENDANCE_MARKED", {
                    "student_id": student_id,
                    "student_name": student_name,
                    "confidence": confidence
                })
            
            return success, message
            
        except Exception as e:
            error_msg = f"Failed to mark attendance: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def process_frame_for_attendance(self, frame):
        """
        Process frame for attendance marking
        
        Args:
            frame: OpenCV frame
            
        Returns:
            tuple: (processed_frame, attendance_results)
        """
        try:
            # Recognize faces
            recognition_results = self.recognize_faces_in_frame(frame)
            
            attendance_results = []
            processed_frame = frame.copy()
            
            for result in recognition_results:
                face_location = result['face_location']
                student_info = result['student_info']
                confidence = result['confidence']
                
                # Draw rectangle around face
                top, right, bottom, left = face_location
                
                if student_info and confidence > 50:  # Minimum confidence threshold
                    # Recognized student
                    color = (0, 255, 0)  # Green
                    label = f"{student_info['full_name']} ({confidence:.1f}%)"
                    
                    # Try to mark attendance
                    success, message = self.mark_attendance_for_recognized_face(student_info, confidence)
                    
                    attendance_results.append({
                        'student_info': student_info,
                        'confidence': confidence,
                        'attendance_marked': success,
                        'message': message
                    })
                    
                else:
                    # Unknown face
                    color = (0, 0, 255)  # Red
                    label = "Unknown"
                    
                    attendance_results.append({
                        'student_info': None,
                        'confidence': confidence,
                        'attendance_marked': False,
                        'message': "Face not recognized"
                    })
                
                # Draw rectangle and label
                cv2.rectangle(processed_frame, (left, top), (right, bottom), color, 2)
                cv2.rectangle(processed_frame, (left, bottom - 35), (right, bottom), color, cv2.FILLED)
                cv2.putText(processed_frame, label, (left + 6, bottom - 6), 
                           cv2.FONT_HERSHEY_DUPLEX, 0.6, (255, 255, 255), 1)
            
            return processed_frame, attendance_results
            
        except Exception as e:
            self.logger.error(f"Frame processing failed: {str(e)}")
            return frame, []
    
    def get_recognition_statistics(self):
        """
        Get recognition statistics
        
        Returns:
            dict: Recognition statistics
        """
        try:
            stats = {
                'known_faces_count': len(self.known_face_encodings),
                'cache_entries': len(self.recognition_cache),
                'tolerance': self.tolerance,
                'model': self.model
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Failed to get recognition statistics: {str(e)}")
            return {}
    
    def clear_recognition_cache(self):
        """Clear recognition cache"""
        self.recognition_cache.clear()
        self.logger.info("Recognition cache cleared")
    
    def update_tolerance(self, new_tolerance):
        """
        Update face recognition tolerance
        
        Args:
            new_tolerance: New tolerance value (0.0 to 1.0)
        """
        if 0.0 <= new_tolerance <= 1.0:
            self.tolerance = new_tolerance
            self.logger.info(f"Face recognition tolerance updated to {new_tolerance}")
        else:
            self.logger.warning(f"Invalid tolerance value: {new_tolerance}")
    
    def find_similar_faces(self, target_encoding, threshold=0.6):
        """
        Find similar faces to a target encoding
        
        Args:
            target_encoding: Target face encoding
            threshold: Similarity threshold
            
        Returns:
            list: List of similar faces with distances
        """
        try:
            if not self.known_face_encodings:
                return []
            
            # Calculate distances
            distances = face_recognition.face_distance(self.known_face_encodings, target_encoding)
            
            # Find similar faces
            similar_faces = []
            for i, distance in enumerate(distances):
                if distance <= threshold:
                    similar_faces.append({
                        'student_info': self.known_face_info[i],
                        'distance': distance,
                        'similarity': (1 - distance) * 100
                    })
            
            # Sort by similarity (lowest distance first)
            similar_faces.sort(key=lambda x: x['distance'])
            
            return similar_faces
            
        except Exception as e:
            self.logger.error(f"Similar face search failed: {str(e)}")
            return []
    
    def validate_face_encoding_uniqueness(self, new_encoding, student_roll_number=None):
        """
        Validate that a face encoding is unique
        
        Args:
            new_encoding: New face encoding to validate
            student_roll_number: Roll number of student (for updates)
            
        Returns:
            tuple: (is_unique, similar_students)
        """
        try:
            similar_faces = self.find_similar_faces(new_encoding, threshold=0.4)
            
            # Filter out the same student if updating
            if student_roll_number:
                similar_faces = [
                    face for face in similar_faces 
                    if face['student_info']['roll_number'] != student_roll_number
                ]
            
            is_unique = len(similar_faces) == 0
            
            return is_unique, similar_faces
            
        except Exception as e:
            self.logger.error(f"Face uniqueness validation failed: {str(e)}")
            return True, []  # Assume unique on error
