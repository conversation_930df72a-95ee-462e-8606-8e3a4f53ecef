"""
Advanced DeepFace Manager for Face Recognition Attendance System
Handles multiple models, automatic fallbacks, and enterprise-level features
"""

import os
import cv2
import numpy as np
import pandas as pd
import threading
import time
import pickle
import json
from datetime import datetime, timedelta
from pathlib import Path
from collections import defaultdict, deque
import config
from utils.logger import setup_logger

class AdvancedDeepFaceManager:
    """Enterprise-level DeepFace manager with advanced features"""
    
    def __init__(self):
        self.logger = setup_logger("AdvancedDeepFaceManager")
        
        # Initialize DeepFace with error handling
        self.deepface_available = self._initialize_deepface()
        
        # Model configuration
        self.available_models = [
            'VGG-Face', 'Facenet', 'Facenet512', 'OpenFace', 
            'DeepFace', 'DeepID', 'ArcFace', 'Dlib', 'SFace'
        ]
        self.available_detectors = [
            'opencv', 'ssd', 'dlib', 'mtcnn', 'retinaface', 'mediapipe'
        ]
        self.available_metrics = ['cosine', 'euclidean', 'euclidean_l2']
        
        # Current configuration
        self.current_model = config.DEEPFACE_MODEL
        self.current_detector = config.DEEPFACE_DETECTOR
        self.current_metric = config.DEEPFACE_DISTANCE_METRIC
        
        # Model performance tracking
        self.model_performance = defaultdict(lambda: {
            'total_time': 0.0,
            'total_calls': 0,
            'success_rate': 0.0,
            'average_confidence': 0.0,
            'last_used': None
        })
        
        # Face database
        self.face_database = {}
        self.database_lock = threading.RLock()
        
        # Caching system
        self.recognition_cache = {}
        self.cache_lock = threading.RLock()
        self.cache_max_size = 1000
        self.cache_ttl = 300  # 5 minutes
        
        # Performance optimization
        self.batch_processing = True
        self.parallel_processing = True
        self.max_workers = 4
        
        # Auto-optimization
        self.auto_optimize = True
        self.optimization_interval = 3600  # 1 hour
        self.last_optimization = datetime.now()
        
        # Load existing face database
        self._load_face_database()
        
        # Start background optimization if enabled
        if self.auto_optimize:
            self._start_optimization_thread()
    
    def _initialize_deepface(self):
        """Initialize DeepFace with comprehensive error handling"""
        try:
            from deepface import DeepFace
            self.DeepFace = DeepFace
            
            # Test basic functionality
            self._test_deepface_functionality()
            
            self.logger.info("DeepFace initialized successfully")
            return True
            
        except ImportError as e:
            self.logger.error(f"DeepFace not available: {e}")
            return False
        except Exception as e:
            self.logger.error(f"DeepFace initialization failed: {e}")
            return False
    
    def _test_deepface_functionality(self):
        """Test DeepFace basic functionality"""
        try:
            # Create test images
            test_img = np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)
            test_path = "test_deepface_init.jpg"
            cv2.imwrite(test_path, test_img)
            
            try:
                # Test representation
                self.DeepFace.represent(
                    img_path=test_path,
                    model_name='VGG-Face',
                    detector_backend='opencv',
                    enforce_detection=False
                )
                self.logger.info("DeepFace functionality test passed")
                
            finally:
                if os.path.exists(test_path):
                    os.remove(test_path)
                    
        except Exception as e:
            self.logger.warning(f"DeepFace functionality test failed: {e}")
    
    def register_face(self, image_data, student_info, force_recompute=False):
        """Register a face with advanced validation and multiple model support"""
        try:
            if not self.deepface_available:
                raise RuntimeError("DeepFace not available")
            
            student_id = student_info['roll_number']
            
            # Save image
            face_dir = config.DEEPFACE_DB_PATH / student_id
            face_dir.mkdir(parents=True, exist_ok=True)
            face_path = face_dir / "face.jpg"
            
            if isinstance(image_data, np.ndarray):
                cv2.imwrite(str(face_path), image_data)
            else:
                # Copy existing file
                import shutil
                shutil.copy2(image_data, face_path)
            
            # Validate face quality
            quality_score = self._assess_face_quality(str(face_path))
            if quality_score < 50:
                self.logger.warning(f"Low quality face for {student_id}: {quality_score}")
            
            # Generate embeddings with multiple models for robustness
            embeddings = {}
            analysis_data = {}
            
            # Primary model
            try:
                embedding = self._get_face_embedding(str(face_path), self.current_model)
                if embedding is not None:
                    embeddings[self.current_model] = embedding
                    
                # Get face analysis
                analysis = self._analyze_face(str(face_path))
                if analysis:
                    analysis_data = analysis
                    
            except Exception as e:
                self.logger.error(f"Primary model {self.current_model} failed: {e}")
            
            # Fallback models
            fallback_models = ['VGG-Face', 'Facenet', 'OpenFace']
            for model in fallback_models:
                if model != self.current_model and model not in embeddings:
                    try:
                        embedding = self._get_face_embedding(str(face_path), model)
                        if embedding is not None:
                            embeddings[model] = embedding
                            break  # One fallback is enough
                    except Exception as e:
                        self.logger.debug(f"Fallback model {model} failed: {e}")
            
            if not embeddings:
                raise RuntimeError("Failed to generate embeddings with any model")
            
            # Store in database
            with self.database_lock:
                self.face_database[student_id] = {
                    'student_info': student_info,
                    'face_path': str(face_path),
                    'embeddings': embeddings,
                    'analysis': analysis_data,
                    'quality_score': quality_score,
                    'registration_date': datetime.now().isoformat(),
                    'last_updated': datetime.now().isoformat()
                }
            
            # Save database
            self._save_face_database()
            
            self.logger.info(f"Face registered for {student_id} with {len(embeddings)} models")
            return True, {
                'quality_score': quality_score,
                'models_used': list(embeddings.keys()),
                'analysis': analysis_data
            }
            
        except Exception as e:
            self.logger.error(f"Face registration failed for {student_info.get('roll_number', 'unknown')}: {e}")
            return False, str(e)
    
    def recognize_face(self, image_data, confidence_threshold=None):
        """Advanced face recognition with multiple model support and caching"""
        try:
            if not self.deepface_available:
                raise RuntimeError("DeepFace not available")
            
            if confidence_threshold is None:
                confidence_threshold = config.DEEPFACE_VERIFICATION_THRESHOLD
            
            # Generate cache key
            cache_key = self._generate_cache_key(image_data)
            
            # Check cache first
            with self.cache_lock:
                if cache_key in self.recognition_cache:
                    cache_entry = self.recognition_cache[cache_key]
                    if time.time() - cache_entry['timestamp'] < self.cache_ttl:
                        self.logger.debug("Cache hit for recognition")
                        return cache_entry['result']
            
            start_time = time.time()
            
            # Save input image
            temp_path = f"temp_recognition_{int(time.time() * 1000)}.jpg"
            if isinstance(image_data, np.ndarray):
                cv2.imwrite(temp_path, image_data)
            else:
                temp_path = image_data
            
            try:
                # Get embedding for input image
                input_embedding = self._get_face_embedding(temp_path, self.current_model)
                if input_embedding is None:
                    return self._create_recognition_result(False, None, 0.0, "No face detected")
                
                # Find best match
                best_match = None
                best_distance = float('inf')
                best_model = None
                
                with self.database_lock:
                    for student_id, face_data in self.face_database.items():
                        embeddings = face_data['embeddings']
                        
                        # Try current model first
                        if self.current_model in embeddings:
                            distance = self._calculate_distance(
                                input_embedding, 
                                embeddings[self.current_model],
                                self.current_metric
                            )
                            
                            if distance < best_distance:
                                best_distance = distance
                                best_match = face_data
                                best_model = self.current_model
                        
                        # Try other models if no good match
                        if best_distance > confidence_threshold:
                            for model, embedding in embeddings.items():
                                if model != self.current_model:
                                    try:
                                        alt_input_embedding = self._get_face_embedding(temp_path, model)
                                        if alt_input_embedding is not None:
                                            distance = self._calculate_distance(
                                                alt_input_embedding,
                                                embedding,
                                                self.current_metric
                                            )
                                            
                                            if distance < best_distance:
                                                best_distance = distance
                                                best_match = face_data
                                                best_model = model
                                    except Exception as e:
                                        self.logger.debug(f"Alternative model {model} failed: {e}")
                
                # Determine if it's a match
                is_match = best_distance <= confidence_threshold
                confidence = max(0, (1 - best_distance) * 100) if is_match else 0
                
                # Create result
                result = self._create_recognition_result(
                    is_match,
                    best_match['student_info'] if best_match else None,
                    confidence,
                    f"Recognized with {best_model}" if is_match else "No match found",
                    {
                        'distance': best_distance,
                        'model_used': best_model,
                        'processing_time': time.time() - start_time,
                        'threshold': confidence_threshold
                    }
                )
                
                # Cache result
                with self.cache_lock:
                    if len(self.recognition_cache) >= self.cache_max_size:
                        self._cleanup_cache()
                    
                    self.recognition_cache[cache_key] = {
                        'result': result,
                        'timestamp': time.time()
                    }
                
                # Update performance stats
                self._update_performance_stats(best_model, time.time() - start_time, is_match, confidence)
                
                return result
                
            finally:
                if isinstance(image_data, np.ndarray) and os.path.exists(temp_path):
                    os.remove(temp_path)
                    
        except Exception as e:
            self.logger.error(f"Face recognition failed: {e}")
            return self._create_recognition_result(False, None, 0.0, f"Error: {str(e)}")
    
    def _get_face_embedding(self, image_path, model_name):
        """Get face embedding with error handling"""
        try:
            result = self.DeepFace.represent(
                img_path=image_path,
                model_name=model_name,
                detector_backend=self.current_detector,
                enforce_detection=False
            )
            
            if result and len(result) > 0:
                return np.array(result[0]["embedding"])
            return None
            
        except Exception as e:
            self.logger.debug(f"Embedding generation failed with {model_name}: {e}")
            return None
    
    def _analyze_face(self, image_path):
        """Analyze face for additional attributes"""
        try:
            analysis = self.DeepFace.analyze(
                img_path=image_path,
                actions=['age', 'gender', 'emotion', 'race'],
                detector_backend=self.current_detector,
                enforce_detection=False
            )
            
            if analysis and len(analysis) > 0:
                return analysis[0]
            return None
            
        except Exception as e:
            self.logger.debug(f"Face analysis failed: {e}")
            return None
    
    def _assess_face_quality(self, image_path):
        """Assess face image quality"""
        try:
            image = cv2.imread(image_path)
            if image is None:
                return 0
            
            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Calculate sharpness (Laplacian variance)
            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
            
            # Calculate brightness
            brightness = np.mean(gray)
            
            # Calculate contrast
            contrast = gray.std()
            
            # Normalize scores
            sharpness_score = min(100, laplacian_var / 10)
            brightness_score = 100 - abs(brightness - 128) / 1.28
            contrast_score = min(100, contrast / 2)
            
            # Overall quality score
            quality_score = (sharpness_score + brightness_score + contrast_score) / 3
            
            return max(0, min(100, quality_score))
            
        except Exception as e:
            self.logger.error(f"Quality assessment failed: {e}")
            return 50  # Default medium quality
    
    def _calculate_distance(self, embedding1, embedding2, metric):
        """Calculate distance between embeddings"""
        try:
            if metric == 'cosine':
                from sklearn.metrics.pairwise import cosine_distances
                return cosine_distances([embedding1], [embedding2])[0][0]
            elif metric == 'euclidean':
                return np.linalg.norm(embedding1 - embedding2)
            elif metric == 'euclidean_l2':
                return np.linalg.norm(embedding1 - embedding2) / np.linalg.norm(embedding1)
            else:
                # Default to cosine
                from sklearn.metrics.pairwise import cosine_distances
                return cosine_distances([embedding1], [embedding2])[0][0]
                
        except Exception as e:
            self.logger.error(f"Distance calculation failed: {e}")
            return float('inf')
    
    def _generate_cache_key(self, image_data):
        """Generate cache key for image"""
        try:
            if isinstance(image_data, np.ndarray):
                return str(hash(image_data.tobytes()))
            else:
                with open(image_data, 'rb') as f:
                    return str(hash(f.read()))
        except Exception:
            return str(time.time())
    
    def _create_recognition_result(self, recognized, student_info, confidence, message, metadata=None):
        """Create standardized recognition result"""
        return {
            'recognized': recognized,
            'student_info': student_info,
            'confidence': confidence,
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'metadata': metadata or {}
        }
    
    def _update_performance_stats(self, model, processing_time, success, confidence):
        """Update model performance statistics"""
        try:
            stats = self.model_performance[model]
            stats['total_time'] += processing_time
            stats['total_calls'] += 1
            stats['last_used'] = datetime.now()
            
            if success:
                # Update success rate
                current_successes = stats['success_rate'] * (stats['total_calls'] - 1) / 100
                new_success_rate = (current_successes + 1) / stats['total_calls'] * 100
                stats['success_rate'] = new_success_rate
                
                # Update average confidence
                current_avg_conf = stats['average_confidence']
                stats['average_confidence'] = (current_avg_conf * (stats['total_calls'] - 1) + confidence) / stats['total_calls']
            
        except Exception as e:
            self.logger.error(f"Performance stats update failed: {e}")
    
    def _cleanup_cache(self):
        """Clean up old cache entries"""
        try:
            current_time = time.time()
            expired_keys = [
                key for key, value in self.recognition_cache.items()
                if current_time - value['timestamp'] > self.cache_ttl
            ]
            
            for key in expired_keys:
                del self.recognition_cache[key]
            
            # If still too large, remove oldest entries
            if len(self.recognition_cache) >= self.cache_max_size:
                sorted_items = sorted(
                    self.recognition_cache.items(),
                    key=lambda x: x[1]['timestamp']
                )
                
                # Remove oldest 20%
                remove_count = len(sorted_items) // 5
                for i in range(remove_count):
                    del self.recognition_cache[sorted_items[i][0]]
                    
        except Exception as e:
            self.logger.error(f"Cache cleanup failed: {e}")
    
    def _load_face_database(self):
        """Load face database from disk"""
        try:
            db_file = config.DEEPFACE_DB_PATH / "face_database.json"
            if db_file.exists():
                with open(db_file, 'r') as f:
                    data = json.load(f)
                
                # Convert embeddings back to numpy arrays
                for student_id, face_data in data.items():
                    if 'embeddings' in face_data:
                        for model, embedding in face_data['embeddings'].items():
                            face_data['embeddings'][model] = np.array(embedding)
                
                self.face_database = data
                self.logger.info(f"Loaded {len(self.face_database)} faces from database")
            
        except Exception as e:
            self.logger.error(f"Failed to load face database: {e}")
            self.face_database = {}
    
    def _save_face_database(self):
        """Save face database to disk"""
        try:
            db_file = config.DEEPFACE_DB_PATH / "face_database.json"
            db_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Convert numpy arrays to lists for JSON serialization
            data_to_save = {}
            for student_id, face_data in self.face_database.items():
                data_copy = face_data.copy()
                if 'embeddings' in data_copy:
                    data_copy['embeddings'] = {
                        model: embedding.tolist()
                        for model, embedding in data_copy['embeddings'].items()
                    }
                data_to_save[student_id] = data_copy
            
            with open(db_file, 'w') as f:
                json.dump(data_to_save, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Failed to save face database: {e}")
    
    def _start_optimization_thread(self):
        """Start background optimization thread"""
        def optimization_loop():
            while True:
                try:
                    time.sleep(self.optimization_interval)
                    self.optimize_performance()
                except Exception as e:
                    self.logger.error(f"Optimization thread error: {e}")
        
        optimization_thread = threading.Thread(target=optimization_loop, daemon=True)
        optimization_thread.start()
        self.logger.info("Background optimization thread started")
    
    def optimize_performance(self):
        """Optimize performance based on usage patterns"""
        try:
            self.logger.info("Running performance optimization...")
            
            # Find best performing model
            best_model = None
            best_score = 0
            
            for model, stats in self.model_performance.items():
                if stats['total_calls'] > 10:  # Minimum calls for reliable stats
                    # Score based on success rate and speed
                    avg_time = stats['total_time'] / stats['total_calls']
                    score = stats['success_rate'] / (1 + avg_time)  # Higher is better
                    
                    if score > best_score:
                        best_score = score
                        best_model = model
            
            # Switch to best model if significantly better
            if best_model and best_model != self.current_model:
                if best_score > 1.2 * self._get_current_model_score():
                    self.logger.info(f"Switching to better performing model: {best_model}")
                    self.current_model = best_model
            
            # Clean up cache
            self._cleanup_cache()
            
            # Update last optimization time
            self.last_optimization = datetime.now()
            
        except Exception as e:
            self.logger.error(f"Performance optimization failed: {e}")
    
    def _get_current_model_score(self):
        """Get current model performance score"""
        try:
            stats = self.model_performance[self.current_model]
            if stats['total_calls'] > 0:
                avg_time = stats['total_time'] / stats['total_calls']
                return stats['success_rate'] / (1 + avg_time)
            return 0
        except Exception:
            return 0
    
    def get_performance_report(self):
        """Get comprehensive performance report"""
        try:
            report = {
                'current_configuration': {
                    'model': self.current_model,
                    'detector': self.current_detector,
                    'metric': self.current_metric
                },
                'database_stats': {
                    'total_faces': len(self.face_database),
                    'cache_size': len(self.recognition_cache),
                    'cache_hit_rate': self._calculate_cache_hit_rate()
                },
                'model_performance': dict(self.model_performance),
                'system_status': {
                    'deepface_available': self.deepface_available,
                    'auto_optimize': self.auto_optimize,
                    'last_optimization': self.last_optimization.isoformat()
                }
            }
            
            return report
            
        except Exception as e:
            self.logger.error(f"Performance report generation failed: {e}")
            return {}
    
    def _calculate_cache_hit_rate(self):
        """Calculate cache hit rate (simplified)"""
        # This would need to be tracked more precisely in a real implementation
        return 0.0
    
    def reload_database(self):
        """Reload face database"""
        with self.database_lock:
            self._load_face_database()
        self.logger.info("Face database reloaded")
    
    def clear_cache(self):
        """Clear recognition cache"""
        with self.cache_lock:
            self.recognition_cache.clear()
        self.logger.info("Recognition cache cleared")
    
    def change_model(self, model_name, detector_backend=None, distance_metric=None):
        """Change recognition model and settings"""
        try:
            if model_name in self.available_models:
                self.current_model = model_name
                
            if detector_backend and detector_backend in self.available_detectors:
                self.current_detector = detector_backend
                
            if distance_metric and distance_metric in self.available_metrics:
                self.current_metric = distance_metric
            
            # Clear cache when changing models
            self.clear_cache()
            
            self.logger.info(f"Changed configuration: model={self.current_model}, detector={self.current_detector}, metric={self.current_metric}")
            return True
            
        except Exception as e:
            self.logger.error(f"Model change failed: {e}")
            return False
