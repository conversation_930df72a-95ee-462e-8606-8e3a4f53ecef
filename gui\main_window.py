"""
Main Window for Face Recognition Attendance System
Central hub for all system operations
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import config
from utils.logger import log_system_event, setup_logger
from utils.helper import UIHelper
from gui.register import StudentRegistrationWindow
from gui.recognize import AttendanceWindow
from gui.reports import ReportsWindow
from gui.admin_panel import AdminPanelWindow

class MainWindow:
    """Main application window"""
    
    def __init__(self, parent, user_data, logout_callback):
        self.parent = parent
        self.user_data = user_data
        self.logout_callback = logout_callback
        self.logger = setup_logger("MainWindow")
        
        # Setup main window
        self.setup_main_window()
        
        # Setup UI
        self.setup_ui()
        
        # Update status
        self.update_status()
        
        # Start status update timer
        self.update_timer()
    
    def setup_main_window(self):
        """Setup main window properties"""
        self.parent.title("Face Recognition Attendance System - Dashboard")
        self.parent.configure(bg=config.BACKGROUND_COLOR)
        
        # Create main menu
        self.create_menu()
    
    def create_menu(self):
        """Create application menu"""
        menubar = tk.Menu(self.parent)
        self.parent.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Logout", command=self.logout)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.parent.quit)
        
        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Student Registration", command=self.open_registration)
        tools_menu.add_command(label="Take Attendance", command=self.open_attendance)
        tools_menu.add_command(label="View Reports", command=self.open_reports)
        tools_menu.add_command(label="Admin Panel", command=self.open_admin_panel)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
    
    def setup_ui(self):
        """Setup the main UI"""
        # Header frame
        header_frame = tk.Frame(self.parent, bg=config.THEME_COLOR, height=80)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        # Header content
        header_content = tk.Frame(header_frame, bg=config.THEME_COLOR)
        header_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # System title
        title_label = tk.Label(
            header_content,
            text="Face Recognition Attendance System",
            font=('Arial', 20, 'bold'),
            fg='white',
            bg=config.THEME_COLOR
        )
        title_label.pack(side=tk.LEFT)
        
        # User info
        user_info = tk.Label(
            header_content,
            text=f"Welcome, {self.user_data['full_name']}",
            font=('Arial', 12),
            fg='white',
            bg=config.THEME_COLOR
        )
        user_info.pack(side=tk.RIGHT)
        
        # Main content frame
        content_frame = tk.Frame(self.parent, bg=config.BACKGROUND_COLOR)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Dashboard grid
        self.create_dashboard_grid(content_frame)
        
        # Status bar
        self.create_status_bar()
    
    def create_dashboard_grid(self, parent):
        """Create dashboard with action buttons"""
        # Dashboard title
        dashboard_title = tk.Label(
            parent,
            text="Dashboard",
            font=('Arial', 16, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        dashboard_title.pack(pady=(0, 20))
        
        # Grid frame
        grid_frame = tk.Frame(parent, bg=config.BACKGROUND_COLOR)
        grid_frame.pack(fill=tk.BOTH, expand=True)
        
        # Configure grid
        for i in range(2):
            grid_frame.grid_columnconfigure(i, weight=1)
        for i in range(2):
            grid_frame.grid_rowconfigure(i, weight=1)
        
        # Action buttons
        self.create_action_button(
            grid_frame, 
            "Student Registration",
            "Register new students\nand capture face data",
            "👤",
            self.open_registration,
            0, 0
        )
        
        self.create_action_button(
            grid_frame,
            "Take Attendance", 
            "Mark attendance using\nface recognition",
            "📷",
            self.open_attendance,
            0, 1
        )
        
        self.create_action_button(
            grid_frame,
            "View Reports",
            "Generate and view\nattendance reports", 
            "📊",
            self.open_reports,
            1, 0
        )
        
        self.create_action_button(
            grid_frame,
            "Admin Panel",
            "Manage students\nand system settings",
            "⚙️",
            self.open_admin_panel,
            1, 1
        )
    
    def create_action_button(self, parent, title, description, icon, command, row, col):
        """Create an action button for the dashboard"""
        # Button frame
        button_frame = tk.Frame(
            parent,
            bg='white',
            relief=tk.RAISED,
            bd=1,
            cursor='hand2'
        )
        button_frame.grid(row=row, column=col, padx=10, pady=10, sticky='nsew')
        
        # Icon label
        icon_label = tk.Label(
            button_frame,
            text=icon,
            font=('Arial', 48),
            bg='white',
            fg=config.THEME_COLOR
        )
        icon_label.pack(pady=(20, 10))
        
        # Title label
        title_label = tk.Label(
            button_frame,
            text=title,
            font=('Arial', 14, 'bold'),
            bg='white',
            fg=config.TEXT_COLOR
        )
        title_label.pack()
        
        # Description label
        desc_label = tk.Label(
            button_frame,
            text=description,
            font=('Arial', 10),
            bg='white',
            fg='gray',
            justify=tk.CENTER
        )
        desc_label.pack(pady=(5, 20))
        
        # Bind click events
        def on_click(event):
            command()
        
        def on_enter(event):
            button_frame.config(bg='#f0f0f0')
            icon_label.config(bg='#f0f0f0')
            title_label.config(bg='#f0f0f0')
            desc_label.config(bg='#f0f0f0')
        
        def on_leave(event):
            button_frame.config(bg='white')
            icon_label.config(bg='white')
            title_label.config(bg='white')
            desc_label.config(bg='white')
        
        # Bind events to all widgets
        for widget in [button_frame, icon_label, title_label, desc_label]:
            widget.bind('<Button-1>', on_click)
            widget.bind('<Enter>', on_enter)
            widget.bind('<Leave>', on_leave)
    
    def create_status_bar(self):
        """Create status bar"""
        self.status_frame = tk.Frame(self.parent, bg='lightgray', height=25)
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM)
        self.status_frame.pack_propagate(False)
        
        # Status label
        self.status_label = tk.Label(
            self.status_frame,
            text="Ready",
            font=('Arial', 9),
            bg='lightgray',
            fg=config.TEXT_COLOR
        )
        self.status_label.pack(side=tk.LEFT, padx=10, pady=2)
        
        # Time label
        self.time_label = tk.Label(
            self.status_frame,
            text="",
            font=('Arial', 9),
            bg='lightgray',
            fg=config.TEXT_COLOR
        )
        self.time_label.pack(side=tk.RIGHT, padx=10, pady=2)
    
    def update_status(self, message="Ready"):
        """Update status bar message"""
        self.status_label.config(text=message)
    
    def update_timer(self):
        """Update time display"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=current_time)
        
        # Schedule next update
        self.parent.after(1000, self.update_timer)
    
    def open_registration(self):
        """Open student registration window"""
        try:
            self.update_status("Opening Student Registration...")
            registration_window = StudentRegistrationWindow(self.parent)
            log_system_event("WINDOW_OPEN", "Student Registration window opened")
        except Exception as e:
            self.logger.error(f"Failed to open registration window: {str(e)}")
            UIHelper.show_error_message("Error", "Failed to open Student Registration")
        finally:
            self.update_status("Ready")
    
    def open_attendance(self):
        """Open attendance window"""
        try:
            self.update_status("Opening Attendance System...")
            attendance_window = AttendanceWindow(self.parent)
            log_system_event("WINDOW_OPEN", "Attendance window opened")
        except Exception as e:
            self.logger.error(f"Failed to open attendance window: {str(e)}")
            UIHelper.show_error_message("Error", "Failed to open Attendance System")
        finally:
            self.update_status("Ready")
    
    def open_reports(self):
        """Open reports window"""
        try:
            self.update_status("Opening Reports...")
            reports_window = ReportsWindow(self.parent)
            log_system_event("WINDOW_OPEN", "Reports window opened")
        except Exception as e:
            self.logger.error(f"Failed to open reports window: {str(e)}")
            UIHelper.show_error_message("Error", "Failed to open Reports")
        finally:
            self.update_status("Ready")
    
    def open_admin_panel(self):
        """Open admin panel"""
        try:
            self.update_status("Opening Admin Panel...")
            admin_window = AdminPanelWindow(self.parent, self.user_data)
            log_system_event("WINDOW_OPEN", "Admin Panel opened")
        except Exception as e:
            self.logger.error(f"Failed to open admin panel: {str(e)}")
            UIHelper.show_error_message("Error", "Failed to open Admin Panel")
        finally:
            self.update_status("Ready")
    
    def logout(self):
        """Handle logout"""
        if UIHelper.ask_yes_no("Logout", "Are you sure you want to logout?"):
            log_system_event("LOGOUT", f"User {self.user_data['username']} logged out")
            self.logout_callback()
    
    def show_about(self):
        """Show about dialog"""
        about_text = """Face Recognition Attendance System
Version 1.0

A comprehensive attendance management system
using facial recognition technology.

Features:
• Student Registration with Face Capture
• Real-time Face Recognition
• Attendance Tracking and Reports
• Admin Panel for Management

Developed using Python, OpenCV, and Tkinter
© 2025 Face Recognition Attendance System"""
        
        messagebox.showinfo("About", about_text)
