"""
Face Recognition Attendance System
Main Application Entry Point

Author: AI Assistant
Date: 2025-06-24
Version: 1.0
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

# Import configuration
import config

# Import GUI modules
from gui.login import LoginWindow
from gui.main_window import MainWindow
from utils.database_manager import DatabaseManager
from utils.logger import setup_logger

class FaceAttendanceApp:
    """Main application class for Face Recognition Attendance System"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.withdraw()  # Hide main window initially
        self.current_user = None
        self.logger = setup_logger()
        
        # Initialize database
        self.db_manager = DatabaseManager()
        self.db_manager.initialize_database()
        
        # Set up the application
        self.setup_application()
        
    def setup_application(self):
        """Setup the main application"""
        self.root.title("Face Recognition Attendance System")
        self.root.geometry(f"{config.WINDOW_WIDTH}x{config.WINDOW_HEIGHT}")
        self.root.configure(bg=config.BACKGROUND_COLOR)
        
        # Center the window
        self.center_window()
        
        # Set window icon (if available)
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass  # Icon file not found, continue without it
            
        # Configure style
        self.setup_styles()
        
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        
    def setup_styles(self):
        """Setup ttk styles for consistent appearance"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure styles
        style.configure('Title.TLabel', 
                       font=('Arial', 16, 'bold'),
                       foreground=config.THEME_COLOR,
                       background=config.BACKGROUND_COLOR)
        
        style.configure('Heading.TLabel',
                       font=('Arial', 12, 'bold'),
                       foreground=config.TEXT_COLOR,
                       background=config.BACKGROUND_COLOR)
        
        style.configure('Custom.TButton',
                       font=('Arial', 10),
                       foreground='white',
                       background=config.BUTTON_COLOR)
        
    def show_login(self):
        """Show login window"""
        login_window = LoginWindow(self.root, self.on_login_success)
        
    def on_login_success(self, user_data):
        """Callback for successful login"""
        self.current_user = user_data
        self.logger.info(f"User {user_data['username']} logged in successfully")
        
        # Show main window
        self.root.deiconify()
        self.main_window = MainWindow(self.root, self.current_user, self.on_logout)
        
    def on_logout(self):
        """Handle user logout"""
        if self.current_user:
            self.logger.info(f"User {self.current_user['username']} logged out")
            self.current_user = None
            
        # Hide main window and show login
        self.root.withdraw()
        self.show_login()
        
    def run(self):
        """Start the application"""
        try:
            self.logger.info("Starting Face Recognition Attendance System")
            self.show_login()
            self.root.mainloop()
        except Exception as e:
            self.logger.error(f"Application error: {str(e)}")
            messagebox.showerror("Error", f"Application error: {str(e)}")
        finally:
            self.logger.info("Application closed")

def main():
    """Main function"""
    try:
        app = FaceAttendanceApp()
        app.run()
    except Exception as e:
        print(f"Failed to start application: {str(e)}")
        messagebox.showerror("Startup Error", f"Failed to start application: {str(e)}")

if __name__ == "__main__":
    main()
