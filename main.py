"""
Face Recognition Attendance System
Main Application Entry Point

Author: AI Assistant
Date: 2025-06-24
Version: 1.0
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

# Import configuration
import config

# Import GUI modules
from gui.login import LoginWindow
from gui.main_window import MainWindow
from utils.database_manager import DatabaseManager
from utils.logger import setup_logger

class FaceAttendanceApp:
    """Main application class for Face Recognition Attendance System"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.withdraw()  # Hide main window initially
        self.current_user = None
        self.logger = setup_logger()

        # Initialize performance monitoring
        if config.ENABLE_PERFORMANCE_MONITORING:
            try:
                from utils.performance_monitor import performance_monitor
                self.performance_monitor = performance_monitor
                self.performance_monitor.start_monitoring()
                self.logger.info("Performance monitoring enabled")
            except ImportError:
                self.logger.warning("Performance monitoring not available")
                self.performance_monitor = None
        else:
            self.performance_monitor = None

        # Initialize notification system
        try:
            from utils.notification_system import notification_system
            self.notification_system = notification_system
            self.notification_system.start_processing()
            self.logger.info("Notification system enabled")
        except ImportError:
            self.logger.warning("Notification system not available")
            self.notification_system = None

        # Initialize database
        self.db_manager = DatabaseManager()
        self.db_manager.initialize_database()

        # Optimize database on startup
        self.db_manager.optimize_database()

        # Set up the application
        self.setup_application()

        # Schedule periodic maintenance
        self.schedule_maintenance()

    def schedule_maintenance(self):
        """Schedule periodic maintenance tasks"""
        try:
            # Schedule database optimization every hour
            self.root.after(3600000, self.perform_maintenance)  # 1 hour in milliseconds

            # Schedule cache cleanup every 30 minutes
            self.root.after(1800000, self.cleanup_caches)  # 30 minutes

            # Schedule backup every 6 hours if enabled
            if config.AUTO_BACKUP_ENABLED:
                self.root.after(21600000, self.perform_backup)  # 6 hours

        except Exception as e:
            self.logger.error(f"Maintenance scheduling failed: {e}")

    def perform_maintenance(self):
        """Perform periodic maintenance"""
        try:
            self.logger.info("Performing scheduled maintenance")

            # Optimize database
            self.db_manager.optimize_database()

            # Clean up old data if configured
            if hasattr(config, 'DATA_RETENTION_DAYS'):
                self.db_manager.cleanup_old_data(config.DATA_RETENTION_DAYS)

            # Performance optimization
            if self.performance_monitor:
                self.performance_monitor.optimize_system()

            # Clean up notification history
            if self.notification_system:
                self.notification_system.cleanup_old_history()

            # Schedule next maintenance
            self.root.after(3600000, self.perform_maintenance)

        except Exception as e:
            self.logger.error(f"Maintenance failed: {e}")

    def cleanup_caches(self):
        """Clean up system caches"""
        try:
            # Clean up face recognition cache
            from utils.face_recognizer import FaceRecognizer
            recognizer = FaceRecognizer()
            recognizer.cleanup_old_cache_entries()

            # Schedule next cleanup
            self.root.after(1800000, self.cleanup_caches)

        except Exception as e:
            self.logger.error(f"Cache cleanup failed: {e}")

    def perform_backup(self):
        """Perform automatic backup"""
        try:
            if config.AUTO_BACKUP_ENABLED:
                backup_path = self.db_manager.backup_database()
                if backup_path:
                    self.logger.info(f"Automatic backup created: {backup_path}")
                else:
                    self.logger.error("Automatic backup failed")

            # Schedule next backup
            self.root.after(21600000, self.perform_backup)

        except Exception as e:
            self.logger.error(f"Backup failed: {e}")
        
    def setup_application(self):
        """Setup the main application"""
        self.root.title("Face Recognition Attendance System")
        self.root.geometry(f"{config.WINDOW_WIDTH}x{config.WINDOW_HEIGHT}")
        self.root.configure(bg=config.BACKGROUND_COLOR)
        
        # Center the window
        self.center_window()
        
        # Set window icon (if available)
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass  # Icon file not found, continue without it
            
        # Configure style
        self.setup_styles()
        
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        
    def setup_styles(self):
        """Setup ttk styles for consistent appearance"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure styles
        style.configure('Title.TLabel', 
                       font=('Arial', 16, 'bold'),
                       foreground=config.THEME_COLOR,
                       background=config.BACKGROUND_COLOR)
        
        style.configure('Heading.TLabel',
                       font=('Arial', 12, 'bold'),
                       foreground=config.TEXT_COLOR,
                       background=config.BACKGROUND_COLOR)
        
        style.configure('Custom.TButton',
                       font=('Arial', 10),
                       foreground='white',
                       background=config.BUTTON_COLOR)
        
    def show_login(self):
        """Show login window"""
        login_window = LoginWindow(self.root, self.on_login_success)
        
    def on_login_success(self, user_data):
        """Callback for successful login"""
        self.current_user = user_data
        self.logger.info(f"User {user_data['username']} logged in successfully")
        
        # Show main window
        self.root.deiconify()
        self.main_window = MainWindow(self.root, self.current_user, self.on_logout)
        
    def on_logout(self):
        """Handle user logout"""
        if self.current_user:
            self.logger.info(f"User {self.current_user['username']} logged out")
            self.current_user = None

        # Hide main window and show login
        self.root.withdraw()
        self.show_login()

    def cleanup_on_exit(self):
        """Cleanup resources on application exit"""
        try:
            self.logger.info("Cleaning up application resources")

            # Stop performance monitoring
            if self.performance_monitor:
                self.performance_monitor.stop_monitoring()

            # Stop notification processing
            if self.notification_system:
                self.notification_system.stop_processing()

            # Close database connections
            if self.db_manager:
                self.db_manager.close_connection()

            # Final log entry
            self.logger.info("Application cleanup completed")

        except Exception as e:
            print(f"Cleanup error: {e}")  # Use print since logger might be closed
        
    def run(self):
        """Start the application"""
        try:
            self.logger.info("Starting Face Recognition Attendance System")

            # Set up exit handlers
            self.root.protocol("WM_DELETE_WINDOW", self.on_window_close)

            # Show splash screen (optional)
            self.show_splash_screen()

            # Show login
            self.show_login()

            # Start main loop
            self.root.mainloop()

        except KeyboardInterrupt:
            self.logger.info("Application interrupted by user")
        except Exception as e:
            self.logger.error(f"Application error: {str(e)}")
            messagebox.showerror("Critical Error",
                               f"A critical error occurred:\n{str(e)}\n\nThe application will now close.")
        finally:
            self.cleanup_on_exit()

    def on_window_close(self):
        """Handle main window close event"""
        try:
            if messagebox.askokcancel("Exit", "Are you sure you want to exit the application?"):
                self.cleanup_on_exit()
                self.root.destroy()
        except Exception as e:
            self.logger.error(f"Window close error: {e}")
            self.root.destroy()

    def show_splash_screen(self):
        """Show splash screen during startup"""
        try:
            splash = tk.Toplevel()
            splash.title("Loading...")
            splash.geometry("400x300")
            splash.configure(bg=config.BACKGROUND_COLOR)

            # Center splash screen
            splash.update_idletasks()
            x = (splash.winfo_screenwidth() // 2) - (400 // 2)
            y = (splash.winfo_screenheight() // 2) - (300 // 2)
            splash.geometry(f"400x300+{x}+{y}")

            # Remove window decorations
            splash.overrideredirect(True)

            # Add content
            title_label = tk.Label(
                splash,
                text="Face Recognition\nAttendance System",
                font=('Arial', 18, 'bold'),
                fg=config.THEME_COLOR,
                bg=config.BACKGROUND_COLOR
            )
            title_label.pack(pady=50)

            loading_label = tk.Label(
                splash,
                text="Loading system components...",
                font=('Arial', 12),
                fg=config.TEXT_COLOR,
                bg=config.BACKGROUND_COLOR
            )
            loading_label.pack(pady=20)

            version_label = tk.Label(
                splash,
                text="Version 1.0 - Advanced Edition",
                font=('Arial', 10),
                fg='gray',
                bg=config.BACKGROUND_COLOR
            )
            version_label.pack(side=tk.BOTTOM, pady=20)

            # Show splash for 3 seconds
            splash.update()
            splash.after(3000, splash.destroy)

        except Exception as e:
            self.logger.error(f"Splash screen error: {e}")

def main():
    """Main function"""
    try:
        app = FaceAttendanceApp()
        app.run()
    except Exception as e:
        print(f"Failed to start application: {str(e)}")
        messagebox.showerror("Startup Error", f"Failed to start application: {str(e)}")

if __name__ == "__main__":
    main()
