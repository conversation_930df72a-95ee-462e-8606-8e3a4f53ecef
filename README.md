# Face Recognition Attendance System

A comprehensive attendance management system using facial recognition technology built with Python, OpenCV, and Tkinter.

## 🌟 Features

### ✅ Core Functionalities

- **Student Registration Module**
  - Capture and store student face images from webcam
  - Input student details (name, roll no, class, etc.)
  - Automatically encode and store facial data for future recognition
  - Face quality validation and duplicate detection

- **Face Recognition Attendance Module**
  - Detect and recognize registered faces in real time
  - Mark attendance if recognized, and log the date & time
  - Prevent duplicate attendance within the same session
  - Live camera feed with face detection overlay

- **Attendance Report Module**
  - Save attendance logs to SQLite database
  - Generate daily/weekly/monthly reports with filters
  - Export reports to CSV format
  - Student-specific attendance reports

- **Admin Panel**
  - View all registered students
  - Update/Delete student info and face data
  - System information and statistics
  - Database backup functionality

- **Login System**
  - Secure admin authentication
  - Session management
  - User activity logging

### 🎨 GUI Features

- Clean, professional, and responsive interface
- Tabbed navigation for different modules
- Real-time webcam preview
- Status messages and progress indicators
- Comprehensive error handling

### 🧠 Advanced AI Features (DeepFace)

- **Multiple AI Models**: VGG-Face, Facenet, OpenFace, DeepFace, ArcFace, and more
- **Advanced Face Detection**: Multiple detector backends (OpenCV, SSD, MTCNN, RetinaFace)
- **High Accuracy**: State-of-the-art deep learning models for superior recognition
- **Face Analysis**: Age, gender, emotion, and ethnicity detection
- **Flexible Distance Metrics**: Cosine, Euclidean, and Euclidean L2 distance calculations
- **Model Benchmarking**: Compare performance across different AI models
- **Real-time Processing**: Optimized for live video recognition

## 🛠️ Technologies Used

- **Python 3.x** - Core programming language
- **OpenCV** - Computer vision and image processing
- **DeepFace** - Advanced AI-powered face recognition (Primary)
- **TensorFlow** - Deep learning framework for DeepFace
- **face_recognition** - Traditional face recognition (Fallback)
- **Tkinter** - GUI framework
- **SQLite3** - Database management
- **Pandas** - Data processing and report generation
- **PIL (Pillow)** - Image processing
- **NumPy** - Numerical computations

## 📁 Project Structure

```
FaceAttendanceSystem/
├── main.py                 # Main application entry point
├── config.py              # Configuration settings
├── requirements.txt       # Python dependencies
├── README.md             # Project documentation
├── gui/                  # GUI modules
│   ├── __init__.py
│   ├── login.py          # Login window
│   ├── main_window.py    # Main dashboard
│   ├── register.py       # Student registration
│   ├── recognize.py      # Attendance recognition
│   ├── reports.py        # Reports generation
│   └── admin_panel.py    # Admin management
├── utils/                # Utility modules
│   ├── __init__.py
│   ├── database_manager.py  # Database operations
│   ├── face_encoder.py      # Face encoding utilities
│   ├── face_recognizer.py   # Face recognition engine
│   ├── helper.py           # Helper functions
│   └── logger.py           # Logging utilities
├── database/             # Database files
│   └── attendance.db     # SQLite database
├── faces/                # Student face images
│   └── [student_roll]/   # Individual student folders
├── attendance/           # Attendance reports
│   └── [reports].csv     # Generated reports
└── system.log           # Application logs
```

## 🚀 Installation and Setup

### Prerequisites

- Python 3.7 or higher
- Webcam/Camera device
- Windows/Linux/macOS

### Step 1: Clone or Download

Download the project files to your local machine.

### Step 2: Install Dependencies

```bash
# Navigate to project directory
cd "Face Recognition System"

# Install required packages
pip install -r requirements.txt
```

### Step 3: Install Additional Dependencies

For face_recognition library, you may need to install additional dependencies:

**Windows:**
```bash
pip install cmake
pip install dlib
pip install face-recognition
```

**Linux/macOS:**
```bash
# Install cmake and dlib first
sudo apt-get install cmake
# or
brew install cmake

pip install dlib
pip install face-recognition
```

### Step 4: Run the Application

```bash
python main.py
```

## 🔧 Configuration

Edit `config.py` to customize system settings:

```python
# Face recognition settings
TOLERANCE = 0.6  # Recognition tolerance (lower = more strict)
MODEL = "hog"    # Face detection model: 'hog' or 'cnn'

# DeepFace settings (Advanced AI)
USE_DEEPFACE = True  # Enable DeepFace for better accuracy
DEEPFACE_MODEL = "VGG-Face"  # AI model: VGG-Face, Facenet, ArcFace, etc.
DEEPFACE_DETECTOR = "opencv"  # Detector: opencv, mtcnn, retinaface, etc.
DEEPFACE_DISTANCE_METRIC = "cosine"  # Distance: cosine, euclidean, euclidean_l2

# Camera settings
CAMERA_INDEX = 0  # Default camera index
FRAME_WIDTH = 640
FRAME_HEIGHT = 480

# GUI settings
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
THEME_COLOR = "#2E86AB"
```

### 🧠 DeepFace Model Options

**Recognition Models:**
- `VGG-Face` - Balanced accuracy and speed (Recommended)
- `Facenet` - High accuracy, Google's model
- `Facenet512` - Enhanced version of Facenet
- `ArcFace` - State-of-the-art accuracy
- `OpenFace` - Lightweight, fast processing
- `DeepFace` - Facebook's original model
- `DeepID` - Academic research model
- `SFace` - Optimized for speed

**Face Detectors:**
- `opencv` - Fast, reliable (Default)
- `mtcnn` - High accuracy for difficult conditions
- `retinaface` - Best accuracy, slower
- `ssd` - Good balance of speed and accuracy
- `dlib` - Traditional, reliable
- `mediapipe` - Google's efficient detector

## 👤 Default Login Credentials

- **Username:** admin
- **Password:** admin123

⚠️ **Important:** Change the default password after first login for security.

## 📖 Usage Guide

### 1. Login
- Start the application
- Use default credentials or your admin account
- Click "Login" to access the system

### 2. Register Students
- Navigate to "Student Registration"
- Fill in student details
- Start camera and capture face
- Ensure good lighting and face positioning
- Click "Register Student"

### 3. Take Attendance
- Go to "Take Attendance"
- Start camera and begin recognition
- Students will be automatically recognized
- Attendance is marked in real-time
- View attendance list on the right panel

### 4. Generate Reports
- Access "View Reports"
- Select report type (Daily/Weekly/Monthly/Student)
- Choose date range
- Generate and export reports

### 5. Admin Management
- Use "Admin Panel" for system management
- View/edit/delete student records
- Monitor system statistics
- Backup database

## 🔍 Troubleshooting

### Common Issues

**Camera not working:**
- Check camera permissions
- Try different camera index in settings
- Ensure no other application is using the camera

**Face not detected:**
- Ensure good lighting
- Position face properly in frame
- Check if face is clearly visible
- Try adjusting recognition tolerance

**Installation issues:**
- Install Visual Studio Build Tools (Windows)
- Update pip: `pip install --upgrade pip`
- Install packages one by one if batch install fails

**Database errors:**
- Check file permissions
- Ensure database directory exists
- Try running as administrator

## 📊 System Requirements

### Minimum Requirements
- Python 3.7+
- 4GB RAM
- 1GB free disk space
- Webcam (720p recommended)
- Windows 10/Linux/macOS

### Recommended Requirements
- Python 3.9+
- 8GB RAM
- 2GB free disk space
- HD Webcam (1080p)
- SSD storage for better performance

## 🔒 Security Features

- Password hashing for admin accounts
- Session management and timeouts
- Input validation and sanitization
- SQL injection prevention
- Comprehensive logging and audit trails

## 🚧 Future Enhancements

- [ ] Email notification on attendance
- [ ] Face dataset augmentation for better accuracy
- [ ] Mask detection for COVID-safe campuses
- [ ] Face recognition from uploaded images/videos
- [ ] Multi-language support
- [ ] REST API for mobile app integration
- [ ] Advanced reporting with charts
- [ ] Bulk student import from CSV
- [ ] Attendance scheduling and automation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For support and questions:
- Create an issue in the repository
- Check the troubleshooting section
- Review the documentation

## 🙏 Acknowledgments

- OpenCV community for computer vision tools
- face_recognition library by Adam Geitgey
- Python community for excellent libraries
- Contributors and testers

---

**Version:** 1.0  
**Last Updated:** 2025-06-24  
**Author:** AI Assistant  

Made with ❤️ for educational institutions and organizations.
