"""
DeepFace Integration Test Script for Face Recognition Attendance System
Tests DeepFace functionality and performance
"""

import sys
import os
import time
import numpy as np
from pathlib import Path

# Add project root to Python path
sys.path.append(str(Path(__file__).parent))

def test_deepface_installation():
    """Test if DeepFace is properly installed"""
    print("Testing DeepFace installation...")
    
    try:
        from deepface import DeepFace
        print("✅ DeepFace imported successfully")
        
        # Test basic functionality
        print("Testing DeepFace basic functionality...")
        
        # Create dummy images for testing
        dummy_img1 = np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)
        dummy_img2 = np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)
        
        # Save dummy images
        import cv2
        cv2.imwrite("test_img1.jpg", dummy_img1)
        cv2.imwrite("test_img2.jpg", dummy_img2)
        
        try:
            # Test verification
            result = DeepFace.verify(
                img1_path="test_img1.jpg",
                img2_path="test_img2.jpg",
                enforce_detection=False
            )
            print("✅ DeepFace verification test passed")
            
            # Test face analysis
            analysis = DeepFace.analyze(
                img_path="test_img1.jpg",
                actions=['age', 'gender', 'emotion'],
                enforce_detection=False
            )
            print("✅ DeepFace analysis test passed")
            
            # Test representation
            embedding = DeepFace.represent(
                img_path="test_img1.jpg",
                enforce_detection=False
            )
            print("✅ DeepFace representation test passed")
            
        except Exception as e:
            print(f"⚠️ DeepFace functionality test failed: {e}")
            return False
        
        finally:
            # Clean up test images
            for img in ["test_img1.jpg", "test_img2.jpg"]:
                if os.path.exists(img):
                    os.remove(img)
        
        return True
        
    except ImportError as e:
        print(f"❌ DeepFace import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ DeepFace test failed: {e}")
        return False

def test_deepface_models():
    """Test different DeepFace models"""
    print("\nTesting DeepFace models...")
    
    try:
        from deepface import DeepFace
        
        # Available models
        models = ['VGG-Face', 'Facenet', 'OpenFace', 'DeepFace']
        
        # Create test image
        test_img = np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)
        cv2.imwrite("test_model.jpg", test_img)
        
        working_models = []
        
        for model in models:
            try:
                print(f"  Testing {model}...")
                start_time = time.time()
                
                embedding = DeepFace.represent(
                    img_path="test_model.jpg",
                    model_name=model,
                    enforce_detection=False
                )
                
                processing_time = time.time() - start_time
                
                if embedding:
                    print(f"  ✅ {model} - {processing_time:.2f}s")
                    working_models.append(model)
                else:
                    print(f"  ❌ {model} - No embedding generated")
                    
            except Exception as e:
                print(f"  ❌ {model} - Error: {e}")
        
        # Clean up
        if os.path.exists("test_model.jpg"):
            os.remove("test_model.jpg")
        
        print(f"\nWorking models: {working_models}")
        return len(working_models) > 0
        
    except Exception as e:
        print(f"❌ Model testing failed: {e}")
        return False

def test_deepface_detectors():
    """Test different face detectors"""
    print("\nTesting face detectors...")
    
    try:
        from deepface import DeepFace
        
        # Available detectors
        detectors = ['opencv', 'ssd', 'dlib', 'mtcnn']
        
        # Create test image with a simple face-like pattern
        test_img = np.ones((224, 224, 3), dtype=np.uint8) * 128
        # Add a simple face-like pattern
        cv2.rectangle(test_img, (80, 80), (144, 144), (200, 200, 200), -1)  # Face
        cv2.rectangle(test_img, (90, 100), (100, 110), (0, 0, 0), -1)  # Left eye
        cv2.rectangle(test_img, (124, 100), (134, 110), (0, 0, 0), -1)  # Right eye
        cv2.rectangle(test_img, (105, 120), (119, 130), (0, 0, 0), -1)  # Nose
        cv2.rectangle(test_img, (95, 135), (129, 140), (0, 0, 0), -1)  # Mouth
        
        cv2.imwrite("test_detector.jpg", test_img)
        
        working_detectors = []
        
        for detector in detectors:
            try:
                print(f"  Testing {detector}...")
                start_time = time.time()
                
                faces = DeepFace.extract_faces(
                    img_path="test_detector.jpg",
                    detector_backend=detector,
                    enforce_detection=False
                )
                
                processing_time = time.time() - start_time
                
                print(f"  ✅ {detector} - {len(faces)} faces detected in {processing_time:.2f}s")
                working_detectors.append(detector)
                    
            except Exception as e:
                print(f"  ❌ {detector} - Error: {e}")
        
        # Clean up
        if os.path.exists("test_detector.jpg"):
            os.remove("test_detector.jpg")
        
        print(f"\nWorking detectors: {working_detectors}")
        return len(working_detectors) > 0
        
    except Exception as e:
        print(f"❌ Detector testing failed: {e}")
        return False

def test_deepface_recognizer():
    """Test our DeepFace recognizer implementation"""
    print("\nTesting DeepFace recognizer implementation...")
    
    try:
        from utils.deepface_recognizer import DeepFaceRecognizer
        
        # Initialize recognizer
        recognizer = DeepFaceRecognizer()
        print("✅ DeepFaceRecognizer initialized")
        
        # Test available models
        models = recognizer.get_available_models()
        print(f"✅ Available models: {models}")
        
        # Test available detectors
        detectors = recognizer.get_available_detectors()
        print(f"✅ Available detectors: {detectors}")
        
        # Test performance stats
        stats = recognizer.get_performance_stats()
        print(f"✅ Performance stats: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ DeepFace recognizer test failed: {e}")
        return False

def test_face_encoder_deepface():
    """Test face encoder with DeepFace"""
    print("\nTesting face encoder with DeepFace...")
    
    try:
        # Update config to use DeepFace
        import config
        original_use_deepface = config.USE_DEEPFACE
        config.USE_DEEPFACE = True
        
        from utils.face_encoder import FaceEncoder
        
        # Initialize encoder
        encoder = FaceEncoder()
        print("✅ FaceEncoder with DeepFace initialized")
        
        # Create test image
        test_img = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        # Test face detection
        face_locations, face_encodings = encoder.detect_faces_in_image(test_img)
        print(f"✅ Face detection completed - {len(face_locations)} faces found")
        
        # Restore original config
        config.USE_DEEPFACE = original_use_deepface
        
        return True
        
    except Exception as e:
        print(f"❌ Face encoder DeepFace test failed: {e}")
        return False

def test_face_recognizer_deepface():
    """Test face recognizer with DeepFace"""
    print("\nTesting face recognizer with DeepFace...")
    
    try:
        # Update config to use DeepFace
        import config
        original_use_deepface = config.USE_DEEPFACE
        config.USE_DEEPFACE = True
        
        from utils.face_recognizer import FaceRecognizer
        
        # Initialize recognizer
        recognizer = FaceRecognizer()
        print("✅ FaceRecognizer with DeepFace initialized")
        
        # Create test frame
        test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        # Test face recognition
        results = recognizer.recognize_faces_in_frame(test_frame)
        print(f"✅ Face recognition completed - {len(results)} results")
        
        # Restore original config
        config.USE_DEEPFACE = original_use_deepface
        
        return True
        
    except Exception as e:
        print(f"❌ Face recognizer DeepFace test failed: {e}")
        return False

def benchmark_deepface_vs_face_recognition():
    """Benchmark DeepFace vs face_recognition performance"""
    print("\nBenchmarking DeepFace vs face_recognition...")
    
    try:
        import config
        
        # Create test image
        test_img = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        results = {}
        
        # Test DeepFace
        try:
            config.USE_DEEPFACE = True
            from utils.face_encoder import FaceEncoder
            
            encoder = FaceEncoder()
            start_time = time.time()
            face_locations, face_encodings = encoder.detect_faces_in_image(test_img)
            deepface_time = time.time() - start_time
            
            results['DeepFace'] = {
                'time': deepface_time,
                'faces_found': len(face_locations),
                'success': True
            }
            print(f"✅ DeepFace: {deepface_time:.3f}s, {len(face_locations)} faces")
            
        except Exception as e:
            results['DeepFace'] = {'time': 0, 'faces_found': 0, 'success': False, 'error': str(e)}
            print(f"❌ DeepFace failed: {e}")
        
        # Test face_recognition
        try:
            config.USE_DEEPFACE = False
            from utils.face_encoder import FaceEncoder
            
            encoder = FaceEncoder()
            start_time = time.time()
            face_locations, face_encodings = encoder.detect_faces_in_image(test_img)
            face_recognition_time = time.time() - start_time
            
            results['face_recognition'] = {
                'time': face_recognition_time,
                'faces_found': len(face_locations),
                'success': True
            }
            print(f"✅ face_recognition: {face_recognition_time:.3f}s, {len(face_locations)} faces")
            
        except Exception as e:
            results['face_recognition'] = {'time': 0, 'faces_found': 0, 'success': False, 'error': str(e)}
            print(f"❌ face_recognition failed: {e}")
        
        return results
        
    except Exception as e:
        print(f"❌ Benchmarking failed: {e}")
        return {}

def run_deepface_tests():
    """Run all DeepFace tests"""
    print("=" * 60)
    print("DeepFace Integration Test Suite")
    print("=" * 60)
    
    tests = [
        ("DeepFace Installation", test_deepface_installation),
        ("DeepFace Models", test_deepface_models),
        ("Face Detectors", test_deepface_detectors),
        ("DeepFace Recognizer", test_deepface_recognizer),
        ("Face Encoder Integration", test_face_encoder_deepface),
        ("Face Recognizer Integration", test_face_recognizer_deepface)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"Running {test_name} Test")
        print(f"{'-' * 40}")
        
        try:
            if test_func():
                print(f"✅ {test_name} test PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"❌ {test_name} test ERROR: {e}")
    
    # Run benchmark
    print(f"\n{'-' * 40}")
    print("Performance Benchmark")
    print(f"{'-' * 40}")
    benchmark_results = benchmark_deepface_vs_face_recognition()
    
    print(f"\n{'=' * 60}")
    print(f"Test Results: {passed}/{total} tests passed")
    print(f"{'=' * 60}")
    
    if passed == total:
        print("🎉 All DeepFace tests passed! DeepFace integration is working correctly.")
        print("\n📊 Benchmark Results:")
        for library, result in benchmark_results.items():
            if result['success']:
                print(f"  {library}: {result['time']:.3f}s")
            else:
                print(f"  {library}: Failed - {result.get('error', 'Unknown error')}")
    else:
        print("⚠️ Some DeepFace tests failed. Check the issues above.")
        print("\n💡 Troubleshooting Tips:")
        print("1. Install DeepFace: pip install deepface")
        print("2. Install TensorFlow: pip install tensorflow")
        print("3. Ensure sufficient memory for model loading")
        print("4. Check internet connection for model downloads")
    
    return passed == total

if __name__ == "__main__":
    success = run_deepface_tests()
    
    if success:
        print("\n🚀 DeepFace is ready to use in your Face Recognition Attendance System!")
        print("   You can now enable DeepFace in the admin panel settings.")
    else:
        print("\n🔧 Please fix the issues before using DeepFace.")
    
    input("\nPress Enter to exit...")
