"""
Comprehensive Startup Script for Face Recognition Attendance System
Handles installation, testing, and system initialization
"""

import sys
import subprocess
import os
from pathlib import Path
import time

def print_banner():
    """Print startup banner"""
    print("=" * 70)
    print("🎯 Face Recognition Attendance System - Advanced Startup")
    print("=" * 70)
    print("🚀 Initializing comprehensive attendance management system...")
    print("📋 Features: Face Recognition, Real-time Attendance, Reports, Admin Panel")
    print("=" * 70)
    print()

def check_python_compatibility():
    """Check Python version and compatibility"""
    print("🔍 Checking Python compatibility...")
    
    version = sys.version_info
    if version.major == 3 and version.minor >= 7:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - Not compatible")
        print("⚠️  Please install Python 3.7 or higher")
        return False

def install_core_dependencies():
    """Install core dependencies with error handling"""
    print("\n📦 Installing core dependencies...")
    
    # Core packages that are essential
    core_packages = [
        "pip",
        "setuptools",
        "wheel",
        "numpy",
        "opencv-python",
        "Pillow",
        "pandas"
    ]
    
    failed = []
    
    for package in core_packages:
        try:
            print(f"  📥 Installing {package}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", 
                "--upgrade", package
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"  ✅ {package} installed successfully")
        except subprocess.CalledProcessError:
            print(f"  ❌ {package} installation failed")
            failed.append(package)
    
    return failed

def install_advanced_dependencies():
    """Install advanced dependencies"""
    print("\n🔧 Installing advanced dependencies...")
    
    advanced_packages = [
        "psutil",
        "ttkthemes", 
        "scikit-learn",
        "matplotlib",
        "reportlab",
        "openpyxl",
        "colorlog",
        "requests"
    ]
    
    failed = []
    
    for package in advanced_packages:
        try:
            print(f"  📥 Installing {package}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"  ✅ {package} installed successfully")
        except subprocess.CalledProcessError:
            print(f"  ⚠️  {package} installation failed (optional)")
            failed.append(package)
    
    return failed

def install_face_recognition_stack():
    """Install face recognition dependencies with multiple fallback methods"""
    print("\n🤖 Installing face recognition stack...")

    # First try to install DeepFace (modern approach)
    deepface_success = install_deepface()

    # Then try traditional face_recognition as fallback
    face_recognition_success = False
    if not deepface_success:
        face_recognition_success = install_traditional_face_recognition()

    return deepface_success or face_recognition_success

def install_deepface():
    """Install DeepFace and its dependencies"""
    print("\n🧠 Installing DeepFace (Advanced AI Models)...")

    try:
        # Install TensorFlow first
        print("  📥 Installing TensorFlow...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "tensorflow==2.13.0"
        ], timeout=600)  # 10 minute timeout for TensorFlow
        print("  ✅ TensorFlow installed successfully")

        # Install DeepFace
        print("  📥 Installing DeepFace...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "deepface==0.0.79"
        ], timeout=300)  # 5 minute timeout
        print("  ✅ DeepFace installed successfully")

        # Test DeepFace installation
        print("  🧪 Testing DeepFace installation...")
        test_result = subprocess.run([
            sys.executable, "-c",
            "from deepface import DeepFace; print('DeepFace test successful')"
        ], capture_output=True, text=True, timeout=60)

        if test_result.returncode == 0:
            print("  ✅ DeepFace test passed")
            return True
        else:
            print(f"  ⚠️  DeepFace test failed: {test_result.stderr}")
            return False

    except (subprocess.CalledProcessError, subprocess.TimeoutExpired) as e:
        print(f"  ❌ DeepFace installation failed: {e}")
        return False

def install_traditional_face_recognition():
    """Install traditional face_recognition library as fallback"""
    print("\n🔄 Installing traditional face_recognition (fallback)...")

    # Try to install dlib first
    dlib_success = False

    print("  📥 Installing dlib (this may take a while)...")

    # Method 1: Try with pip
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "dlib"
        ], timeout=300)  # 5 minute timeout
        print("  ✅ dlib installed successfully with pip")
        dlib_success = True
    except (subprocess.CalledProcessError, subprocess.TimeoutExpired):
        print("  ⚠️  dlib installation with pip failed")

    # Method 2: Try with conda if pip failed
    if not dlib_success:
        try:
            print("  📥 Trying dlib installation with conda...")
            subprocess.check_call([
                "conda", "install", "-c", "conda-forge", "dlib", "-y"
            ], timeout=300)
            print("  ✅ dlib installed successfully with conda")
            dlib_success = True
        except (subprocess.CalledProcessError, subprocess.TimeoutExpired, FileNotFoundError):
            print("  ⚠️  dlib installation with conda failed")

    # Install face_recognition if dlib succeeded
    if dlib_success:
        try:
            print("  📥 Installing face_recognition...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "face-recognition"
            ], timeout=180)  # 3 minute timeout
            print("  ✅ face_recognition installed successfully")
            return True
        except (subprocess.CalledProcessError, subprocess.TimeoutExpired):
            print("  ❌ face_recognition installation failed")
            return False
    else:
        print("  ❌ Cannot install face_recognition without dlib")
        return False

def setup_project_structure():
    """Setup project directory structure"""
    print("\n📁 Setting up project structure...")
    
    directories = [
        "database",
        "faces", 
        "attendance",
        "backups",
        "reports",
        "logs",
        "temp"
    ]
    
    for directory in directories:
        dir_path = Path(directory)
        if not dir_path.exists():
            dir_path.mkdir(exist_ok=True)
            print(f"  ✅ Created {directory}/ directory")
        else:
            print(f"  ✅ {directory}/ directory exists")

def initialize_database():
    """Initialize the database"""
    print("\n🗄️  Initializing database...")
    
    try:
        # Add current directory to Python path
        sys.path.append(str(Path(__file__).parent))
        
        from utils.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        db_manager.initialize_database()
        print("  ✅ Database initialized successfully")
        
        # Test default admin user
        admin_user = db_manager.verify_admin_login("admin", "admin123")
        if admin_user:
            print("  ✅ Default admin user created (admin/admin123)")
        else:
            print("  ⚠️  Default admin user verification failed")
        
        db_manager.close_connection()
        return True
        
    except Exception as e:
        print(f"  ❌ Database initialization failed: {e}")
        return False

def test_camera_system():
    """Test camera functionality"""
    print("\n📷 Testing camera system...")
    
    try:
        import cv2
        
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            ret, frame = cap.read()
            if ret and frame is not None:
                height, width = frame.shape[:2]
                print(f"  ✅ Camera working - Resolution: {width}x{height}")
                cap.release()
                return True
            else:
                print("  ⚠️  Camera opened but failed to capture frame")
                cap.release()
                return False
        else:
            print("  ⚠️  Camera not accessible (may be in use or not connected)")
            return False
            
    except Exception as e:
        print(f"  ❌ Camera test failed: {e}")
        return False

def run_system_tests():
    """Run comprehensive system tests"""
    print("\n🧪 Running system tests...")
    
    try:
        # Import test module
        sys.path.append(str(Path(__file__).parent))
        import test_system
        
        # Run tests
        success = test_system.run_all_tests()
        
        if success:
            print("  ✅ All system tests passed")
        else:
            print("  ⚠️  Some tests failed - system may have limited functionality")
        
        return success
        
    except Exception as e:
        print(f"  ❌ System testing failed: {e}")
        return False

def create_shortcuts():
    """Create desktop shortcuts and launchers"""
    print("\n🔗 Creating shortcuts...")
    
    try:
        # Create batch file for Windows
        if sys.platform == "win32":
            batch_content = f'''@echo off
title Face Recognition Attendance System
cd /d "{os.getcwd()}"
python main.py
pause'''
            
            with open("start_attendance_system.bat", "w") as f:
                f.write(batch_content)
            print("  ✅ Windows batch file created")
        
        # Create shell script for Unix-like systems
        else:
            shell_content = f'''#!/bin/bash
cd "{os.getcwd()}"
python3 main.py
read -p "Press Enter to continue..."'''
            
            with open("start_attendance_system.sh", "w") as f:
                f.write(shell_content)
            
            # Make executable
            os.chmod("start_attendance_system.sh", 0o755)
            print("  ✅ Shell script created")
        
        return True
        
    except Exception as e:
        print(f"  ⚠️  Shortcut creation failed: {e}")
        return False

def display_final_summary(installation_success, test_success, camera_success):
    """Display final installation summary"""
    print("\n" + "=" * 70)
    print("📊 INSTALLATION SUMMARY")
    print("=" * 70)
    
    print(f"🔧 Core Installation: {'✅ SUCCESS' if installation_success else '❌ FAILED'}")
    print(f"🧪 System Tests: {'✅ PASSED' if test_success else '⚠️  PARTIAL'}")
    print(f"📷 Camera System: {'✅ WORKING' if camera_success else '⚠️  ISSUES'}")
    
    print("\n📋 NEXT STEPS:")
    print("-" * 30)
    
    if installation_success:
        print("1. ✅ Run the application: python main.py")
        print("2. 🔑 Login with: admin / admin123")
        print("3. 👤 Register students in the system")
        print("4. 📊 Start taking attendance")
        
        if sys.platform == "win32":
            print("5. 🚀 Or use: start_attendance_system.bat")
        else:
            print("5. 🚀 Or use: ./start_attendance_system.sh")
    else:
        print("1. ❌ Fix installation issues above")
        print("2. 🔄 Run this script again")
        print("3. 📖 Check README.md for troubleshooting")
    
    if not camera_success:
        print("\n⚠️  CAMERA ISSUES:")
        print("- Check camera permissions")
        print("- Ensure camera is not in use by other apps")
        print("- Try different camera index in settings")
    
    print("\n📚 DOCUMENTATION:")
    print("- README.md - Complete setup guide")
    print("- PROJECT_OVERVIEW.md - Feature overview")
    print("- config.py - System configuration")
    
    print("\n🎉 Face Recognition Attendance System is ready!")
    print("=" * 70)

def main():
    """Main startup function"""
    print_banner()
    
    # Check Python compatibility
    if not check_python_compatibility():
        input("\nPress Enter to exit...")
        return
    
    # Install dependencies
    print("🔄 Starting installation process...")
    
    core_failed = install_core_dependencies()
    advanced_failed = install_advanced_dependencies()
    face_recognition_success = install_face_recognition_stack()
    
    installation_success = len(core_failed) == 0 and face_recognition_success
    
    # Setup project
    setup_project_structure()
    database_success = initialize_database()
    
    # Test systems
    camera_success = test_camera_system()
    test_success = run_system_tests()
    
    # Create shortcuts
    create_shortcuts()
    
    # Final summary
    display_final_summary(
        installation_success and database_success,
        test_success,
        camera_success
    )
    
    # Offer to start the application
    if installation_success and database_success:
        print("\n🚀 Would you like to start the application now? (y/n): ", end="")
        try:
            response = input().lower().strip()
            if response in ['y', 'yes']:
                print("\n🎯 Starting Face Recognition Attendance System...")
                time.sleep(2)
                subprocess.run([sys.executable, "main.py"])
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
