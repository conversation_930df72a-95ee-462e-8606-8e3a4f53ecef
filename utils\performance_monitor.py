"""
Performance Monitoring System for Face Recognition Attendance System
Monitors system performance, resource usage, and provides optimization recommendations
"""

import time
import threading
import psutil
import gc
from datetime import datetime, timedelta
from collections import deque, defaultdict
import config
from utils.logger import setup_logger

class PerformanceMonitor:
    """Advanced performance monitoring system"""
    
    def __init__(self):
        self.logger = setup_logger("PerformanceMonitor")
        self.is_monitoring = False
        self.monitor_thread = None
        
        # Performance metrics storage
        self.metrics = {
            'cpu_usage': deque(maxlen=100),
            'memory_usage': deque(maxlen=100),
            'face_recognition_times': deque(maxlen=100),
            'database_query_times': deque(maxlen=100),
            'camera_fps': deque(maxlen=50),
            'system_errors': deque(maxlen=50)
        }
        
        # Performance thresholds
        self.thresholds = {
            'cpu_usage_warning': config.CPU_USAGE_THRESHOLD,
            'memory_usage_warning': config.MEMORY_USAGE_THRESHOLD,
            'face_recognition_time_warning': 1.0,  # seconds
            'database_query_time_warning': 0.5,  # seconds
            'fps_warning': 15  # minimum FPS
        }
        
        # Performance statistics
        self.stats = {
            'total_face_recognitions': 0,
            'successful_recognitions': 0,
            'failed_recognitions': 0,
            'total_database_queries': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'system_uptime': datetime.now(),
            'last_optimization': None
        }
        
        # Alerts and recommendations
        self.alerts = deque(maxlen=20)
        self.recommendations = []
        
    def start_monitoring(self):
        """Start performance monitoring"""
        if not self.is_monitoring:
            self.is_monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            self.logger.info("Performance monitoring started")
    
    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        self.logger.info("Performance monitoring stopped")
    
    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                # Collect system metrics
                self._collect_system_metrics()
                
                # Check thresholds and generate alerts
                self._check_thresholds()
                
                # Generate recommendations
                self._generate_recommendations()
                
                # Sleep for monitoring interval
                time.sleep(5)  # Monitor every 5 seconds
                
            except Exception as e:
                self.logger.error(f"Performance monitoring error: {e}")
                time.sleep(10)  # Wait longer on error
    
    def _collect_system_metrics(self):
        """Collect system performance metrics"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            self.metrics['cpu_usage'].append({
                'timestamp': datetime.now(),
                'value': cpu_percent
            })
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_mb = memory.used / (1024 * 1024)
            self.metrics['memory_usage'].append({
                'timestamp': datetime.now(),
                'value': memory_mb,
                'percent': memory.percent
            })
            
            # Disk usage
            disk = psutil.disk_usage('/')
            
            # Network I/O (if needed)
            # net_io = psutil.net_io_counters()
            
        except Exception as e:
            self.logger.error(f"System metrics collection failed: {e}")
    
    def record_face_recognition_time(self, processing_time, success=True):
        """Record face recognition performance"""
        try:
            self.metrics['face_recognition_times'].append({
                'timestamp': datetime.now(),
                'value': processing_time,
                'success': success
            })
            
            self.stats['total_face_recognitions'] += 1
            if success:
                self.stats['successful_recognitions'] += 1
            else:
                self.stats['failed_recognitions'] += 1
                
        except Exception as e:
            self.logger.error(f"Face recognition time recording failed: {e}")
    
    def record_database_query_time(self, query_time, query_type="unknown"):
        """Record database query performance"""
        try:
            self.metrics['database_query_times'].append({
                'timestamp': datetime.now(),
                'value': query_time,
                'type': query_type
            })
            
            self.stats['total_database_queries'] += 1
            
        except Exception as e:
            self.logger.error(f"Database query time recording failed: {e}")
    
    def record_camera_fps(self, fps):
        """Record camera FPS"""
        try:
            self.metrics['camera_fps'].append({
                'timestamp': datetime.now(),
                'value': fps
            })
            
        except Exception as e:
            self.logger.error(f"Camera FPS recording failed: {e}")
    
    def record_cache_hit(self, hit=True):
        """Record cache performance"""
        try:
            if hit:
                self.stats['cache_hits'] += 1
            else:
                self.stats['cache_misses'] += 1
                
        except Exception as e:
            self.logger.error(f"Cache recording failed: {e}")
    
    def record_system_error(self, error_type, error_message):
        """Record system errors"""
        try:
            self.metrics['system_errors'].append({
                'timestamp': datetime.now(),
                'type': error_type,
                'message': error_message
            })
            
        except Exception as e:
            self.logger.error(f"System error recording failed: {e}")
    
    def _check_thresholds(self):
        """Check performance thresholds and generate alerts"""
        try:
            current_time = datetime.now()
            
            # Check CPU usage
            if self.metrics['cpu_usage']:
                latest_cpu = self.metrics['cpu_usage'][-1]['value']
                if latest_cpu > self.thresholds['cpu_usage_warning']:
                    self._add_alert(
                        'HIGH_CPU_USAGE',
                        f"CPU usage is {latest_cpu:.1f}% (threshold: {self.thresholds['cpu_usage_warning']}%)",
                        'warning'
                    )
            
            # Check memory usage
            if self.metrics['memory_usage']:
                latest_memory = self.metrics['memory_usage'][-1]['value']
                if latest_memory > self.thresholds['memory_usage_warning']:
                    self._add_alert(
                        'HIGH_MEMORY_USAGE',
                        f"Memory usage is {latest_memory:.1f}MB (threshold: {self.thresholds['memory_usage_warning']}MB)",
                        'warning'
                    )
            
            # Check face recognition performance
            if self.metrics['face_recognition_times']:
                recent_times = [
                    m['value'] for m in list(self.metrics['face_recognition_times'])[-10:]
                ]
                avg_time = sum(recent_times) / len(recent_times)
                if avg_time > self.thresholds['face_recognition_time_warning']:
                    self._add_alert(
                        'SLOW_FACE_RECOGNITION',
                        f"Average face recognition time is {avg_time:.2f}s (threshold: {self.thresholds['face_recognition_time_warning']}s)",
                        'warning'
                    )
            
            # Check camera FPS
            if self.metrics['camera_fps']:
                recent_fps = [
                    m['value'] for m in list(self.metrics['camera_fps'])[-5:]
                ]
                avg_fps = sum(recent_fps) / len(recent_fps)
                if avg_fps < self.thresholds['fps_warning']:
                    self._add_alert(
                        'LOW_CAMERA_FPS',
                        f"Camera FPS is {avg_fps:.1f} (threshold: {self.thresholds['fps_warning']})",
                        'warning'
                    )
            
        except Exception as e:
            self.logger.error(f"Threshold checking failed: {e}")
    
    def _add_alert(self, alert_type, message, severity='info'):
        """Add performance alert"""
        try:
            alert = {
                'timestamp': datetime.now(),
                'type': alert_type,
                'message': message,
                'severity': severity
            }
            
            self.alerts.append(alert)
            
            if severity in ['warning', 'error']:
                self.logger.warning(f"Performance Alert [{alert_type}]: {message}")
            
        except Exception as e:
            self.logger.error(f"Alert addition failed: {e}")
    
    def _generate_recommendations(self):
        """Generate performance optimization recommendations"""
        try:
            self.recommendations.clear()
            
            # CPU optimization recommendations
            if self.metrics['cpu_usage']:
                avg_cpu = sum(m['value'] for m in list(self.metrics['cpu_usage'])[-20:]) / min(20, len(self.metrics['cpu_usage']))
                if avg_cpu > 70:
                    self.recommendations.append({
                        'type': 'CPU_OPTIMIZATION',
                        'priority': 'high',
                        'message': 'Consider reducing face recognition frequency or using a more efficient model',
                        'actions': [
                            'Increase frame skip count',
                            'Use HOG model instead of CNN',
                            'Reduce image resolution for processing'
                        ]
                    })
            
            # Memory optimization recommendations
            if self.metrics['memory_usage']:
                avg_memory = sum(m['value'] for m in list(self.metrics['memory_usage'])[-20:]) / min(20, len(self.metrics['memory_usage']))
                if avg_memory > 400:  # 400MB
                    self.recommendations.append({
                        'type': 'MEMORY_OPTIMIZATION',
                        'priority': 'medium',
                        'message': 'Memory usage is high, consider optimization',
                        'actions': [
                            'Clear recognition cache more frequently',
                            'Reduce batch size for face processing',
                            'Enable garbage collection optimization'
                        ]
                    })
            
            # Database optimization recommendations
            if self.stats['total_database_queries'] > 1000:
                cache_hit_rate = self.stats['cache_hits'] / (self.stats['cache_hits'] + self.stats['cache_misses']) if (self.stats['cache_hits'] + self.stats['cache_misses']) > 0 else 0
                if cache_hit_rate < 0.8:
                    self.recommendations.append({
                        'type': 'DATABASE_OPTIMIZATION',
                        'priority': 'medium',
                        'message': f'Cache hit rate is {cache_hit_rate:.1%}, consider optimization',
                        'actions': [
                            'Increase cache size',
                            'Optimize database indexes',
                            'Use connection pooling'
                        ]
                    })
            
        except Exception as e:
            self.logger.error(f"Recommendation generation failed: {e}")
    
    def get_performance_summary(self):
        """Get comprehensive performance summary"""
        try:
            summary = {
                'system_uptime': datetime.now() - self.stats['system_uptime'],
                'current_metrics': {},
                'averages': {},
                'statistics': self.stats.copy(),
                'recent_alerts': list(self.alerts)[-5:],
                'recommendations': self.recommendations.copy()
            }
            
            # Current metrics
            if self.metrics['cpu_usage']:
                summary['current_metrics']['cpu_usage'] = self.metrics['cpu_usage'][-1]['value']
            
            if self.metrics['memory_usage']:
                summary['current_metrics']['memory_usage'] = self.metrics['memory_usage'][-1]['value']
            
            if self.metrics['camera_fps']:
                summary['current_metrics']['camera_fps'] = self.metrics['camera_fps'][-1]['value']
            
            # Calculate averages
            for metric_name, metric_data in self.metrics.items():
                if metric_data and metric_name not in ['system_errors']:
                    values = [m['value'] for m in metric_data if 'value' in m]
                    if values:
                        summary['averages'][metric_name] = {
                            'average': sum(values) / len(values),
                            'min': min(values),
                            'max': max(values),
                            'count': len(values)
                        }
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Performance summary generation failed: {e}")
            return {}
    
    def optimize_system(self):
        """Perform automatic system optimization"""
        try:
            optimizations_performed = []
            
            # Force garbage collection
            gc.collect()
            optimizations_performed.append("Garbage collection performed")
            
            # Clear old metrics
            cutoff_time = datetime.now() - timedelta(hours=1)
            for metric_name, metric_data in self.metrics.items():
                original_count = len(metric_data)
                # Keep only recent data
                while metric_data and metric_data[0].get('timestamp', datetime.now()) < cutoff_time:
                    metric_data.popleft()
                
                if len(metric_data) < original_count:
                    optimizations_performed.append(f"Cleared {original_count - len(metric_data)} old {metric_name} entries")
            
            # Update optimization timestamp
            self.stats['last_optimization'] = datetime.now()
            
            self.logger.info(f"System optimization completed: {', '.join(optimizations_performed)}")
            return optimizations_performed
            
        except Exception as e:
            self.logger.error(f"System optimization failed: {e}")
            return []
    
    def export_performance_report(self, output_path=None):
        """Export detailed performance report"""
        try:
            if output_path is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = config.REPORTS_DIR / f"performance_report_{timestamp}.txt"
            
            summary = self.get_performance_summary()
            
            with open(output_path, 'w') as f:
                f.write("Face Recognition Attendance System - Performance Report\n")
                f.write("=" * 60 + "\n\n")
                
                f.write(f"Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"System Uptime: {summary.get('system_uptime', 'Unknown')}\n\n")
                
                # Current metrics
                f.write("Current Metrics:\n")
                f.write("-" * 20 + "\n")
                for metric, value in summary.get('current_metrics', {}).items():
                    f.write(f"{metric}: {value}\n")
                f.write("\n")
                
                # Averages
                f.write("Average Performance:\n")
                f.write("-" * 20 + "\n")
                for metric, data in summary.get('averages', {}).items():
                    f.write(f"{metric}:\n")
                    f.write(f"  Average: {data['average']:.2f}\n")
                    f.write(f"  Min: {data['min']:.2f}\n")
                    f.write(f"  Max: {data['max']:.2f}\n")
                    f.write(f"  Samples: {data['count']}\n\n")
                
                # Statistics
                f.write("System Statistics:\n")
                f.write("-" * 20 + "\n")
                for stat, value in summary.get('statistics', {}).items():
                    f.write(f"{stat}: {value}\n")
                f.write("\n")
                
                # Recent alerts
                f.write("Recent Alerts:\n")
                f.write("-" * 20 + "\n")
                for alert in summary.get('recent_alerts', []):
                    f.write(f"[{alert['timestamp']}] {alert['type']}: {alert['message']}\n")
                f.write("\n")
                
                # Recommendations
                f.write("Optimization Recommendations:\n")
                f.write("-" * 30 + "\n")
                for rec in summary.get('recommendations', []):
                    f.write(f"Priority: {rec['priority'].upper()}\n")
                    f.write(f"Type: {rec['type']}\n")
                    f.write(f"Message: {rec['message']}\n")
                    f.write("Actions:\n")
                    for action in rec['actions']:
                        f.write(f"  - {action}\n")
                    f.write("\n")
            
            return str(output_path)
            
        except Exception as e:
            self.logger.error(f"Performance report export failed: {e}")
            return None

# Global performance monitor instance
performance_monitor = PerformanceMonitor()
