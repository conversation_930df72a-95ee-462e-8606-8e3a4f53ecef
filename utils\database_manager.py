"""
Database Manager for Face Recognition Attendance System
Handles all database operations including schema creation and data management
"""

import sqlite3
import hashlib
import pickle
import base64
from datetime import datetime, date
from pathlib import Path
import config

class DatabaseManager:
    """Manages all database operations for the attendance system"""
    
    def __init__(self):
        self.db_path = config.DATABASE_PATH
        self.connection = None
        
    def get_connection(self):
        """Get database connection"""
        if self.connection is None:
            self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
            self.connection.row_factory = sqlite3.Row
        return self.connection
    
    def close_connection(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def initialize_database(self):
        """Initialize database with required tables"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Create admin users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS admin_users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                email TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        # Create students table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS students (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                roll_number TEXT UNIQUE NOT NULL,
                full_name TEXT NOT NULL,
                class_name TEXT NOT NULL,
                section TEXT,
                email TEXT,
                phone TEXT,
                address TEXT,
                face_encoding BLOB,
                photo_path TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        # Create attendance table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS attendance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                student_id INTEGER NOT NULL,
                attendance_date DATE NOT NULL,
                attendance_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'Present',
                confidence_score REAL,
                marked_by TEXT DEFAULT 'System',
                notes TEXT,
                FOREIGN KEY (student_id) REFERENCES students (id),
                UNIQUE(student_id, attendance_date)
            )
        ''')
        
        # Create system settings table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_key TEXT UNIQUE NOT NULL,
                setting_value TEXT NOT NULL,
                description TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create attendance sessions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS attendance_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_name TEXT NOT NULL,
                start_time TIMESTAMP NOT NULL,
                end_time TIMESTAMP,
                created_by TEXT NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        
        # Create default admin user if not exists
        self.create_default_admin()
        
        # Insert default system settings
        self.insert_default_settings()
    
    def create_default_admin(self):
        """Create default admin user"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Check if admin exists
        cursor.execute("SELECT COUNT(*) FROM admin_users WHERE username = ?", ("admin",))
        if cursor.fetchone()[0] == 0:
            # Create default admin
            password_hash = self.hash_password("admin123")
            cursor.execute('''
                INSERT INTO admin_users (username, password_hash, full_name, email)
                VALUES (?, ?, ?, ?)
            ''', ("admin", password_hash, "System Administrator", "<EMAIL>"))
            conn.commit()
    
    def insert_default_settings(self):
        """Insert default system settings"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        default_settings = [
            ("face_recognition_tolerance", "0.6", "Face recognition tolerance level"),
            ("min_attendance_interval", "300", "Minimum seconds between attendance marks"),
            ("camera_index", "0", "Default camera index"),
            ("email_notifications", "false", "Enable email notifications"),
            ("auto_backup", "true", "Enable automatic database backup")
        ]
        
        for key, value, description in default_settings:
            cursor.execute('''
                INSERT OR IGNORE INTO system_settings (setting_key, setting_value, description)
                VALUES (?, ?, ?)
            ''', (key, value, description))
        
        conn.commit()
    
    @staticmethod
    def hash_password(password):
        """Hash password using SHA-256"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_admin_login(self, username, password):
        """Verify admin login credentials"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        password_hash = self.hash_password(password)
        cursor.execute('''
            SELECT id, username, full_name, email FROM admin_users 
            WHERE username = ? AND password_hash = ? AND is_active = 1
        ''', (username, password_hash))
        
        user = cursor.fetchone()
        if user:
            # Update last login
            cursor.execute('''
                UPDATE admin_users SET last_login = CURRENT_TIMESTAMP 
                WHERE id = ?
            ''', (user['id'],))
            conn.commit()
            return dict(user)
        return None
    
    def add_student(self, student_data, face_encoding=None):
        """Add new student to database"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Serialize face encoding if provided
        face_encoding_blob = None
        if face_encoding is not None:
            face_encoding_blob = pickle.dumps(face_encoding)
        
        cursor.execute('''
            INSERT INTO students (roll_number, full_name, class_name, section, 
                                email, phone, address, face_encoding, photo_path)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            student_data['roll_number'],
            student_data['full_name'],
            student_data['class_name'],
            student_data.get('section', ''),
            student_data.get('email', ''),
            student_data.get('phone', ''),
            student_data.get('address', ''),
            face_encoding_blob,
            student_data.get('photo_path', '')
        ))
        
        conn.commit()
        return cursor.lastrowid

    def get_student_by_roll_number(self, roll_number):
        """Get student by roll number"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM students WHERE roll_number = ? AND is_active = 1
        ''', (roll_number,))

        return cursor.fetchone()

    def get_all_students(self):
        """Get all active students"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM students WHERE is_active = 1 ORDER BY full_name
        ''')

        return cursor.fetchall()

    def get_student_face_encodings(self):
        """Get all student face encodings for recognition"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, roll_number, full_name, face_encoding
            FROM students WHERE face_encoding IS NOT NULL AND is_active = 1
        ''')

        students = cursor.fetchall()
        encodings = []
        student_info = []

        for student in students:
            if student['face_encoding']:
                encoding = pickle.loads(student['face_encoding'])
                encodings.append(encoding)
                student_info.append({
                    'id': student['id'],
                    'roll_number': student['roll_number'],
                    'full_name': student['full_name']
                })

        return encodings, student_info

    def mark_attendance(self, student_id, confidence_score=None):
        """Mark attendance for a student"""
        conn = self.get_connection()
        cursor = conn.cursor()

        today = date.today()

        # Check if attendance already marked today
        cursor.execute('''
            SELECT id FROM attendance
            WHERE student_id = ? AND attendance_date = ?
        ''', (student_id, today))

        if cursor.fetchone():
            return False, "Attendance already marked for today"

        # Mark attendance
        cursor.execute('''
            INSERT INTO attendance (student_id, attendance_date, confidence_score)
            VALUES (?, ?, ?)
        ''', (student_id, today, confidence_score))

        conn.commit()
        return True, "Attendance marked successfully"

    def get_attendance_by_date(self, attendance_date):
        """Get attendance records for a specific date"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT a.*, s.roll_number, s.full_name, s.class_name, s.section
            FROM attendance a
            JOIN students s ON a.student_id = s.id
            WHERE a.attendance_date = ?
            ORDER BY a.attendance_time
        ''', (attendance_date,))

        return cursor.fetchall()

    def get_attendance_by_student(self, student_id, start_date=None, end_date=None):
        """Get attendance records for a specific student"""
        conn = self.get_connection()
        cursor = conn.cursor()

        query = '''
            SELECT a.*, s.roll_number, s.full_name, s.class_name
            FROM attendance a
            JOIN students s ON a.student_id = s.id
            WHERE a.student_id = ?
        '''
        params = [student_id]

        if start_date:
            query += " AND a.attendance_date >= ?"
            params.append(start_date)

        if end_date:
            query += " AND a.attendance_date <= ?"
            params.append(end_date)

        query += " ORDER BY a.attendance_date DESC"

        cursor.execute(query, params)
        return cursor.fetchall()

    def get_attendance_statistics(self, start_date=None, end_date=None):
        """Get attendance statistics"""
        conn = self.get_connection()
        cursor = conn.cursor()

        # Total students
        cursor.execute("SELECT COUNT(*) as total FROM students WHERE is_active = 1")
        total_students = cursor.fetchone()['total']

        # Attendance query
        query = '''
            SELECT
                COUNT(DISTINCT a.student_id) as present_students,
                COUNT(a.id) as total_attendance_records,
                a.attendance_date
            FROM attendance a
        '''
        params = []

        if start_date:
            query += " WHERE a.attendance_date >= ?"
            params.append(start_date)

        if end_date:
            if start_date:
                query += " AND a.attendance_date <= ?"
            else:
                query += " WHERE a.attendance_date <= ?"
            params.append(end_date)

        query += " GROUP BY a.attendance_date ORDER BY a.attendance_date DESC"

        cursor.execute(query, params)
        attendance_data = cursor.fetchall()

        return {
            'total_students': total_students,
            'attendance_data': attendance_data
        }

    def update_student(self, student_id, student_data, face_encoding=None):
        """Update student information"""
        conn = self.get_connection()
        cursor = conn.cursor()

        # Prepare update query
        update_fields = []
        params = []

        for field in ['full_name', 'class_name', 'section', 'email', 'phone', 'address']:
            if field in student_data:
                update_fields.append(f"{field} = ?")
                params.append(student_data[field])

        if face_encoding is not None:
            update_fields.append("face_encoding = ?")
            params.append(pickle.dumps(face_encoding))

        if update_fields:
            update_fields.append("updated_at = CURRENT_TIMESTAMP")
            params.append(student_id)

            query = f"UPDATE students SET {', '.join(update_fields)} WHERE id = ?"
            cursor.execute(query, params)
            conn.commit()
            return True

        return False

    def delete_student(self, student_id):
        """Soft delete student (mark as inactive)"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            UPDATE students SET is_active = 0, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (student_id,))

        conn.commit()
        return cursor.rowcount > 0
