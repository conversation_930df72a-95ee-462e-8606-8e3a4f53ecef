"""
Advanced Real-time Recognition Engine for Face Recognition Attendance System
Multi-threaded, optimized engine for real-time face recognition with advanced features
"""

import cv2
import numpy as np
import threading
import queue
import time
from datetime import datetime, timedelta
from collections import deque, defaultdict
import concurrent.futures
import config
from utils.advanced_deepface_manager import AdvancedDeepFaceManager
from utils.database_manager import DatabaseManager
from utils.logger import setup_logger

class RealtimeRecognitionEngine:
    """Advanced real-time face recognition engine with multi-threading and optimization"""
    
    def __init__(self):
        self.logger = setup_logger("RealtimeRecognitionEngine")
        
        # Initialize components
        self.deepface_manager = AdvancedDeepFaceManager()
        self.db_manager = DatabaseManager()
        
        # Threading configuration
        self.max_workers = config.MAX_CONCURRENT_RECOGNITIONS
        self.frame_queue = queue.Queue(maxsize=10)
        self.result_queue = queue.Queue()
        self.processing_threads = []
        self.is_running = False
        
        # Performance optimization
        self.frame_skip_count = config.FRAME_SKIP_COUNT
        self.frame_counter = 0
        self.resize_factor = config.IMAGE_RESIZE_FACTOR
        
        # Recognition state management
        self.active_recognitions = {}
        self.recognition_lock = threading.RLock()
        self.duplicate_prevention = {}
        self.duplicate_timeout = config.DUPLICATE_DETECTION_WINDOW
        
        # Performance tracking
        self.performance_metrics = {
            'frames_processed': 0,
            'faces_detected': 0,
            'successful_recognitions': 0,
            'average_fps': 0.0,
            'average_processing_time': 0.0,
            'queue_size_history': deque(maxlen=100)
        }
        
        # Face detection optimization
        self.face_cascade = cv2.CascadeClassifier(
            cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
        )
        self.detection_scale_factor = config.FACE_DETECTION_SCALE_FACTOR
        self.detection_min_neighbors = config.FACE_DETECTION_MIN_NEIGHBORS
        self.detection_min_size = config.FACE_DETECTION_MIN_SIZE
        
        # Advanced features
        self.enable_face_tracking = True
        self.face_trackers = {}
        self.tracker_id_counter = 0
        
        # Quality filtering
        self.min_face_size = 80
        self.max_face_size = 400
        self.quality_threshold = 50
        
        self.logger.info("Real-time recognition engine initialized")
    
    def start(self):
        """Start the recognition engine"""
        try:
            if self.is_running:
                self.logger.warning("Engine already running")
                return
            
            self.is_running = True
            
            # Start processing threads
            for i in range(self.max_workers):
                thread = threading.Thread(
                    target=self._processing_worker,
                    name=f"RecognitionWorker-{i}",
                    daemon=True
                )
                thread.start()
                self.processing_threads.append(thread)
            
            # Start performance monitoring thread
            monitor_thread = threading.Thread(
                target=self._performance_monitor,
                name="PerformanceMonitor",
                daemon=True
            )
            monitor_thread.start()
            
            self.logger.info(f"Recognition engine started with {self.max_workers} workers")
            
        except Exception as e:
            self.logger.error(f"Failed to start recognition engine: {e}")
            self.stop()
    
    def stop(self):
        """Stop the recognition engine"""
        try:
            self.is_running = False
            
            # Clear queues
            while not self.frame_queue.empty():
                try:
                    self.frame_queue.get_nowait()
                except queue.Empty:
                    break
            
            while not self.result_queue.empty():
                try:
                    self.result_queue.get_nowait()
                except queue.Empty:
                    break
            
            # Wait for threads to finish
            for thread in self.processing_threads:
                thread.join(timeout=2)
            
            self.processing_threads.clear()
            self.logger.info("Recognition engine stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping recognition engine: {e}")
    
    def process_frame(self, frame, session_id=None):
        """Process a video frame for face recognition"""
        try:
            if not self.is_running:
                return frame, []
            
            # Frame skipping for performance
            self.frame_counter += 1
            if self.frame_counter % (self.frame_skip_count + 1) != 0:
                return frame, []
            
            # Resize frame for faster processing
            if self.resize_factor != 1.0:
                height, width = frame.shape[:2]
                new_width = int(width * self.resize_factor)
                new_height = int(height * self.resize_factor)
                processing_frame = cv2.resize(frame, (new_width, new_height))
            else:
                processing_frame = frame.copy()
            
            # Add frame to processing queue
            frame_data = {
                'frame': processing_frame,
                'original_frame': frame,
                'timestamp': time.time(),
                'session_id': session_id,
                'frame_id': self.frame_counter
            }
            
            try:
                self.frame_queue.put_nowait(frame_data)
            except queue.Full:
                # Queue is full, skip this frame
                self.logger.debug("Frame queue full, skipping frame")
                return frame, []
            
            # Get results if available
            results = self._get_available_results()
            
            # Draw results on frame
            annotated_frame = self._draw_results_on_frame(frame, results)
            
            return annotated_frame, results
            
        except Exception as e:
            self.logger.error(f"Frame processing error: {e}")
            return frame, []
    
    def _processing_worker(self):
        """Worker thread for processing frames"""
        while self.is_running:
            try:
                # Get frame from queue
                frame_data = self.frame_queue.get(timeout=1)
                
                if frame_data is None:
                    continue
                
                # Process the frame
                results = self._process_single_frame(frame_data)
                
                # Add results to result queue
                if results:
                    result_data = {
                        'results': results,
                        'frame_id': frame_data['frame_id'],
                        'timestamp': frame_data['timestamp'],
                        'session_id': frame_data.get('session_id')
                    }
                    
                    try:
                        self.result_queue.put_nowait(result_data)
                    except queue.Full:
                        # Result queue full, remove oldest result
                        try:
                            self.result_queue.get_nowait()
                            self.result_queue.put_nowait(result_data)
                        except queue.Empty:
                            pass
                
                # Mark task as done
                self.frame_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"Processing worker error: {e}")
    
    def _process_single_frame(self, frame_data):
        """Process a single frame for face recognition"""
        try:
            start_time = time.time()
            frame = frame_data['frame']
            
            # Detect faces
            face_locations = self._detect_faces_optimized(frame)
            
            if not face_locations:
                return []
            
            results = []
            
            # Process each detected face
            for face_location in face_locations:
                try:
                    # Extract face region
                    x, y, w, h = face_location
                    face_region = frame[y:y+h, x:x+w]
                    
                    # Quality check
                    quality_score = self._assess_face_quality(face_region)
                    if quality_score < self.quality_threshold:
                        continue
                    
                    # Check for duplicates
                    if self._is_duplicate_detection(face_location, frame_data['session_id']):
                        continue
                    
                    # Recognize face
                    recognition_result = self.deepface_manager.recognize_face(face_region)
                    
                    if recognition_result['recognized']:
                        # Record attendance if recognized
                        student_info = recognition_result['student_info']
                        attendance_recorded = self._record_attendance(
                            student_info,
                            frame_data['session_id'],
                            recognition_result['confidence']
                        )
                        
                        recognition_result['attendance_recorded'] = attendance_recorded
                        
                        # Update duplicate prevention
                        self._update_duplicate_prevention(
                            face_location,
                            student_info['roll_number'],
                            frame_data['session_id']
                        )
                    
                    # Add face location to result
                    recognition_result['face_location'] = face_location
                    recognition_result['quality_score'] = quality_score
                    
                    results.append(recognition_result)
                    
                except Exception as e:
                    self.logger.error(f"Face processing error: {e}")
                    continue
            
            # Update performance metrics
            processing_time = time.time() - start_time
            self._update_performance_metrics(len(face_locations), len(results), processing_time)
            
            return results
            
        except Exception as e:
            self.logger.error(f"Single frame processing error: {e}")
            return []
    
    def _detect_faces_optimized(self, frame):
        """Optimized face detection"""
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # Histogram equalization for better detection
            gray = cv2.equalizeHist(gray)
            
            # Detect faces
            faces = self.face_cascade.detectMultiScale(
                gray,
                scaleFactor=self.detection_scale_factor,
                minNeighbors=self.detection_min_neighbors,
                minSize=self.detection_min_size,
                flags=cv2.CASCADE_SCALE_IMAGE
            )
            
            # Filter faces by size
            filtered_faces = []
            for (x, y, w, h) in faces:
                if self.min_face_size <= w <= self.max_face_size and self.min_face_size <= h <= self.max_face_size:
                    filtered_faces.append((x, y, w, h))
            
            return filtered_faces
            
        except Exception as e:
            self.logger.error(f"Face detection error: {e}")
            return []
    
    def _assess_face_quality(self, face_region):
        """Assess face region quality"""
        try:
            if face_region.size == 0:
                return 0
            
            # Convert to grayscale
            gray = cv2.cvtColor(face_region, cv2.COLOR_BGR2GRAY)
            
            # Calculate sharpness (Laplacian variance)
            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
            
            # Calculate brightness
            brightness = np.mean(gray)
            
            # Calculate contrast
            contrast = gray.std()
            
            # Normalize and combine scores
            sharpness_score = min(100, laplacian_var / 10)
            brightness_score = 100 - abs(brightness - 128) / 1.28
            contrast_score = min(100, contrast / 2)
            
            quality_score = (sharpness_score + brightness_score + contrast_score) / 3
            
            return max(0, min(100, quality_score))
            
        except Exception as e:
            self.logger.error(f"Quality assessment error: {e}")
            return 50
    
    def _is_duplicate_detection(self, face_location, session_id):
        """Check if this is a duplicate detection"""
        try:
            current_time = time.time()
            key = f"{session_id}_{face_location[0]}_{face_location[1]}"
            
            with self.recognition_lock:
                if key in self.duplicate_prevention:
                    last_detection = self.duplicate_prevention[key]
                    if current_time - last_detection['timestamp'] < self.duplicate_timeout:
                        return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Duplicate check error: {e}")
            return False
    
    def _update_duplicate_prevention(self, face_location, student_id, session_id):
        """Update duplicate prevention tracking"""
        try:
            current_time = time.time()
            key = f"{session_id}_{face_location[0]}_{face_location[1]}"
            
            with self.recognition_lock:
                self.duplicate_prevention[key] = {
                    'timestamp': current_time,
                    'student_id': student_id,
                    'face_location': face_location
                }
                
                # Clean up old entries
                expired_keys = [
                    k for k, v in self.duplicate_prevention.items()
                    if current_time - v['timestamp'] > self.duplicate_timeout
                ]
                
                for expired_key in expired_keys:
                    del self.duplicate_prevention[expired_key]
                    
        except Exception as e:
            self.logger.error(f"Duplicate prevention update error: {e}")
    
    def _record_attendance(self, student_info, session_id, confidence):
        """Record attendance for recognized student"""
        try:
            # Check if attendance already recorded today
            today = datetime.now().date()
            existing_attendance = self.db_manager.get_student_attendance_by_date(
                student_info['id'], today
            )
            
            if existing_attendance:
                self.logger.debug(f"Attendance already recorded for {student_info['roll_number']} today")
                return False
            
            # Record new attendance
            attendance_data = {
                'student_id': student_info['id'],
                'attendance_date': today,
                'attendance_time': datetime.now(),
                'status': 'present',
                'confidence_score': confidence,
                'session_id': session_id
            }
            
            success = self.db_manager.add_attendance_record(attendance_data)
            
            if success:
                self.logger.info(f"Attendance recorded for {student_info['full_name']} ({student_info['roll_number']})")
                return True
            else:
                self.logger.error(f"Failed to record attendance for {student_info['roll_number']}")
                return False
                
        except Exception as e:
            self.logger.error(f"Attendance recording error: {e}")
            return False
    
    def _get_available_results(self):
        """Get available recognition results"""
        results = []
        
        try:
            while not self.result_queue.empty():
                try:
                    result_data = self.result_queue.get_nowait()
                    results.extend(result_data['results'])
                except queue.Empty:
                    break
        except Exception as e:
            self.logger.error(f"Result retrieval error: {e}")
        
        return results
    
    def _draw_results_on_frame(self, frame, results):
        """Draw recognition results on frame"""
        try:
            annotated_frame = frame.copy()
            
            for result in results:
                if 'face_location' not in result:
                    continue
                
                x, y, w, h = result['face_location']
                
                # Adjust coordinates if frame was resized
                if self.resize_factor != 1.0:
                    x = int(x / self.resize_factor)
                    y = int(y / self.resize_factor)
                    w = int(w / self.resize_factor)
                    h = int(h / self.resize_factor)
                
                # Determine color based on recognition status
                if result['recognized']:
                    color = (0, 255, 0)  # Green for recognized
                    status = "RECOGNIZED"
                    name = result['student_info']['full_name']
                    confidence = result['confidence']
                    
                    # Check if attendance was recorded
                    if result.get('attendance_recorded', False):
                        status += " ✓"
                        color = (0, 200, 0)
                    
                    label = f"{name} ({confidence:.1f}%)"
                else:
                    color = (0, 0, 255)  # Red for unknown
                    status = "UNKNOWN"
                    label = "Unknown Person"
                
                # Draw rectangle
                cv2.rectangle(annotated_frame, (x, y), (x + w, y + h), color, 2)
                
                # Draw status indicator
                cv2.rectangle(annotated_frame, (x, y - 30), (x + w, y), color, -1)
                cv2.putText(
                    annotated_frame,
                    status,
                    (x + 5, y - 10),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.6,
                    (255, 255, 255),
                    2
                )
                
                # Draw label
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                cv2.rectangle(
                    annotated_frame,
                    (x, y + h),
                    (x + label_size[0] + 10, y + h + 25),
                    color,
                    -1
                )
                cv2.putText(
                    annotated_frame,
                    label,
                    (x + 5, y + h + 18),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.6,
                    (255, 255, 255),
                    2
                )
                
                # Draw quality indicator
                quality = result.get('quality_score', 0)
                quality_color = (0, 255, 0) if quality > 70 else (0, 255, 255) if quality > 50 else (0, 0, 255)
                cv2.circle(annotated_frame, (x + w - 10, y + 10), 5, quality_color, -1)
            
            return annotated_frame
            
        except Exception as e:
            self.logger.error(f"Frame annotation error: {e}")
            return frame
    
    def _update_performance_metrics(self, faces_detected, successful_recognitions, processing_time):
        """Update performance metrics"""
        try:
            self.performance_metrics['frames_processed'] += 1
            self.performance_metrics['faces_detected'] += faces_detected
            self.performance_metrics['successful_recognitions'] += successful_recognitions
            
            # Update average processing time
            current_avg = self.performance_metrics['average_processing_time']
            frame_count = self.performance_metrics['frames_processed']
            
            new_avg = (current_avg * (frame_count - 1) + processing_time) / frame_count
            self.performance_metrics['average_processing_time'] = new_avg
            
            # Update queue size history
            self.performance_metrics['queue_size_history'].append(self.frame_queue.qsize())
            
        except Exception as e:
            self.logger.error(f"Performance metrics update error: {e}")
    
    def _performance_monitor(self):
        """Monitor and log performance metrics"""
        last_frame_count = 0
        last_time = time.time()
        
        while self.is_running:
            try:
                time.sleep(5)  # Update every 5 seconds
                
                current_time = time.time()
                current_frame_count = self.performance_metrics['frames_processed']
                
                # Calculate FPS
                time_diff = current_time - last_time
                frame_diff = current_frame_count - last_frame_count
                
                if time_diff > 0:
                    fps = frame_diff / time_diff
                    self.performance_metrics['average_fps'] = fps
                
                # Log performance
                if frame_diff > 0:
                    self.logger.debug(
                        f"Performance: {fps:.1f} FPS, "
                        f"Avg processing: {self.performance_metrics['average_processing_time']:.3f}s, "
                        f"Queue size: {self.frame_queue.qsize()}"
                    )
                
                last_frame_count = current_frame_count
                last_time = current_time
                
            except Exception as e:
                self.logger.error(f"Performance monitoring error: {e}")
    
    def get_performance_stats(self):
        """Get current performance statistics"""
        try:
            stats = self.performance_metrics.copy()
            stats['queue_size'] = self.frame_queue.qsize()
            stats['result_queue_size'] = self.result_queue.qsize()
            stats['active_workers'] = len([t for t in self.processing_threads if t.is_alive()])
            stats['duplicate_prevention_entries'] = len(self.duplicate_prevention)
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Performance stats error: {e}")
            return {}
    
    def optimize_performance(self):
        """Optimize engine performance based on current metrics"""
        try:
            stats = self.get_performance_stats()
            
            # Adjust frame skip based on queue size
            avg_queue_size = np.mean(list(self.performance_metrics['queue_size_history']))
            
            if avg_queue_size > 5:
                # Queue is getting full, skip more frames
                self.frame_skip_count = min(self.frame_skip_count + 1, 5)
            elif avg_queue_size < 2 and self.frame_skip_count > 0:
                # Queue is empty, process more frames
                self.frame_skip_count = max(self.frame_skip_count - 1, 0)
            
            # Adjust quality threshold based on performance
            if stats['average_processing_time'] > 0.5:
                # Processing is slow, increase quality threshold
                self.quality_threshold = min(self.quality_threshold + 5, 80)
            elif stats['average_processing_time'] < 0.2:
                # Processing is fast, decrease quality threshold
                self.quality_threshold = max(self.quality_threshold - 5, 30)
            
            self.logger.info(f"Performance optimized: frame_skip={self.frame_skip_count}, quality_threshold={self.quality_threshold}")
            
        except Exception as e:
            self.logger.error(f"Performance optimization error: {e}")
    
    def clear_caches(self):
        """Clear all caches"""
        try:
            with self.recognition_lock:
                self.duplicate_prevention.clear()
            
            self.deepface_manager.clear_cache()
            
            self.logger.info("All caches cleared")
            
        except Exception as e:
            self.logger.error(f"Cache clearing error: {e}")
    
    def get_status(self):
        """Get engine status"""
        return {
            'is_running': self.is_running,
            'worker_count': len(self.processing_threads),
            'active_workers': len([t for t in self.processing_threads if t.is_alive()]),
            'frame_queue_size': self.frame_queue.qsize(),
            'result_queue_size': self.result_queue.qsize(),
            'performance_stats': self.get_performance_stats()
        }
