"""
Reports Window for Face Recognition Attendance System
Handles attendance report generation and viewing
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date, timedelta
import pandas as pd
import config
from utils.database_manager import DatabaseManager
from utils.helper import ReportGenerator, DateTimeHelper, UIHelper
from utils.logger import setup_logger, log_system_event

class ReportsWindow:
    """Reports window for viewing and generating attendance reports"""
    
    def __init__(self, parent):
        self.parent = parent
        self.db_manager = DatabaseManager()
        self.report_generator = ReportGenerator(self.db_manager)
        self.logger = setup_logger("ReportsWindow")
        
        # Current report data
        self.current_report_data = None
        
        # Create reports window
        self.window = tk.Toplevel(parent)
        self.window.title("Attendance Reports")
        self.window.geometry("1000x700")
        self.window.configure(bg=config.BACKGROUND_COLOR)
        
        # Center window
        UIHelper.center_window(self.window, 1000, 700)
        
        # Make window modal
        self.window.transient(parent)
        self.window.grab_set()
        
        # Setup UI
        self.setup_ui()
        
        # Load initial data
        self.load_default_report()
        
        # Handle window close
        self.window.protocol("WM_DELETE_WINDOW", self.on_close)
    
    def setup_ui(self):
        """Setup the reports UI"""
        # Main container
        main_frame = tk.Frame(self.window, bg=config.BACKGROUND_COLOR)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Title
        title_label = tk.Label(
            main_frame,
            text="Attendance Reports",
            font=('Arial', 18, 'bold'),
            fg=config.THEME_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        title_label.pack(pady=(0, 20))
        
        # Create layout
        content_frame = tk.Frame(main_frame, bg=config.BACKGROUND_COLOR)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # Left panel - Filters and controls
        self.create_filter_panel(content_frame)
        
        # Right panel - Report display
        self.create_report_panel(content_frame)
        
        # Bottom controls
        self.create_action_panel(main_frame)
    
    def create_filter_panel(self, parent):
        """Create filter panel"""
        # Left frame
        left_frame = tk.Frame(parent, bg=config.BACKGROUND_COLOR, width=300)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_frame.pack_propagate(False)
        
        # Filter title
        filter_title = tk.Label(
            left_frame,
            text="Report Filters",
            font=('Arial', 14, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        filter_title.pack(anchor=tk.W, pady=(0, 15))
        
        # Report type selection
        type_frame = tk.LabelFrame(
            left_frame,
            text="Report Type",
            font=('Arial', 11, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        type_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.report_type_var = tk.StringVar(value="daily")
        
        report_types = [
            ("Daily Report", "daily"),
            ("Weekly Report", "weekly"),
            ("Monthly Report", "monthly"),
            ("Date Range", "range"),
            ("Student Report", "student")
        ]
        
        for text, value in report_types:
            rb = tk.Radiobutton(
                type_frame,
                text=text,
                variable=self.report_type_var,
                value=value,
                font=('Arial', 10),
                fg=config.TEXT_COLOR,
                bg=config.BACKGROUND_COLOR,
                command=self.on_report_type_change
            )
            rb.pack(anchor=tk.W, padx=10, pady=2)
        
        # Date selection
        date_frame = tk.LabelFrame(
            left_frame,
            text="Date Selection",
            font=('Arial', 11, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        date_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Start date
        start_date_label = tk.Label(
            date_frame,
            text="Start Date:",
            font=('Arial', 10),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        start_date_label.pack(anchor=tk.W, padx=10, pady=(10, 5))
        
        self.start_date_var = tk.StringVar(value=date.today().strftime("%Y-%m-%d"))
        self.start_date_entry = tk.Entry(
            date_frame,
            textvariable=self.start_date_var,
            font=('Arial', 10),
            relief=tk.FLAT,
            bd=1
        )
        self.start_date_entry.pack(fill=tk.X, padx=10, pady=(0, 5))
        
        # End date
        end_date_label = tk.Label(
            date_frame,
            text="End Date:",
            font=('Arial', 10),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        end_date_label.pack(anchor=tk.W, padx=10, pady=(5, 5))
        
        self.end_date_var = tk.StringVar(value=date.today().strftime("%Y-%m-%d"))
        self.end_date_entry = tk.Entry(
            date_frame,
            textvariable=self.end_date_var,
            font=('Arial', 10),
            relief=tk.FLAT,
            bd=1,
            state=tk.DISABLED
        )
        self.end_date_entry.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        # Quick date buttons
        quick_dates_frame = tk.Frame(date_frame, bg=config.BACKGROUND_COLOR)
        quick_dates_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        today_btn = tk.Button(
            quick_dates_frame,
            text="Today",
            font=('Arial', 9),
            bg='lightblue',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.set_today
        )
        today_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        yesterday_btn = tk.Button(
            quick_dates_frame,
            text="Yesterday",
            font=('Arial', 9),
            bg='lightblue',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.set_yesterday
        )
        yesterday_btn.pack(side=tk.LEFT, padx=5)
        
        # Student selection (for student reports)
        self.student_frame = tk.LabelFrame(
            left_frame,
            text="Student Selection",
            font=('Arial', 11, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        self.student_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Student combobox
        self.student_var = tk.StringVar()
        self.student_combo = ttk.Combobox(
            self.student_frame,
            textvariable=self.student_var,
            font=('Arial', 10),
            state="readonly"
        )
        self.student_combo.pack(fill=tk.X, padx=10, pady=10)
        
        # Load students
        self.load_students()
        
        # Generate button
        generate_btn = tk.Button(
            left_frame,
            text="Generate Report",
            font=('Arial', 12, 'bold'),
            bg=config.THEME_COLOR,
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.generate_report
        )
        generate_btn.pack(fill=tk.X, pady=(0, 10), ipady=8)
        
        # Statistics frame
        stats_frame = tk.LabelFrame(
            left_frame,
            text="Statistics",
            font=('Arial', 11, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        stats_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.stats_label = tk.Label(
            stats_frame,
            text="No report generated",
            font=('Arial', 10),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR,
            justify=tk.LEFT
        )
        self.stats_label.pack(anchor=tk.W, padx=10, pady=10)
        
        # Initially hide student frame
        self.student_frame.pack_forget()
    
    def create_report_panel(self, parent):
        """Create report display panel"""
        # Right frame
        right_frame = tk.Frame(parent, bg=config.BACKGROUND_COLOR)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))
        
        # Report title
        self.report_title_label = tk.Label(
            right_frame,
            text="Daily Attendance Report",
            font=('Arial', 14, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        self.report_title_label.pack(anchor=tk.W, pady=(0, 10))
        
        # Report info
        self.report_info_label = tk.Label(
            right_frame,
            text="",
            font=('Arial', 11),
            fg='gray',
            bg=config.BACKGROUND_COLOR
        )
        self.report_info_label.pack(anchor=tk.W, pady=(0, 10))
        
        # Report treeview frame
        tree_frame = tk.Frame(right_frame, bg=config.BACKGROUND_COLOR)
        tree_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Report treeview
        self.report_tree = ttk.Treeview(tree_frame, show='headings')
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.report_tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.report_tree.xview)
        
        self.report_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.report_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def create_action_panel(self, parent):
        """Create action panel"""
        action_frame = tk.Frame(parent, bg=config.BACKGROUND_COLOR)
        action_frame.pack(fill=tk.X, pady=(20, 0))
        
        # Export button
        export_btn = tk.Button(
            action_frame,
            text="Export to CSV",
            font=('Arial', 11, 'bold'),
            bg='green',
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.export_report
        )
        export_btn.pack(side=tk.LEFT, padx=(0, 10), ipady=5, ipadx=15)
        
        # Print button
        print_btn = tk.Button(
            action_frame,
            text="Print Report",
            font=('Arial', 11),
            bg='blue',
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.print_report
        )
        print_btn.pack(side=tk.LEFT, padx=10, ipady=5, ipadx=15)
        
        # Refresh button
        refresh_btn = tk.Button(
            action_frame,
            text="Refresh",
            font=('Arial', 11),
            bg='orange',
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.refresh_report
        )
        refresh_btn.pack(side=tk.LEFT, padx=10, ipady=5, ipadx=15)
        
        # Close button
        close_btn = tk.Button(
            action_frame,
            text="Close",
            font=('Arial', 11),
            bg='gray',
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.on_close
        )
        close_btn.pack(side=tk.RIGHT, ipady=5, ipadx=15)

    def on_report_type_change(self):
        """Handle report type change"""
        report_type = self.report_type_var.get()

        if report_type == "range":
            self.end_date_entry.config(state=tk.NORMAL)
        else:
            self.end_date_entry.config(state=tk.DISABLED)

        if report_type == "student":
            self.student_frame.pack(fill=tk.X, pady=(0, 15))
        else:
            self.student_frame.pack_forget()

        # Set appropriate date ranges
        if report_type == "weekly":
            start_date, end_date = DateTimeHelper.get_week_date_range()
            self.start_date_var.set(start_date.strftime("%Y-%m-%d"))
            self.end_date_var.set(end_date.strftime("%Y-%m-%d"))
        elif report_type == "monthly":
            start_date, end_date = DateTimeHelper.get_month_date_range()
            self.start_date_var.set(start_date.strftime("%Y-%m-%d"))
            self.end_date_var.set(end_date.strftime("%Y-%m-%d"))

    def load_students(self):
        """Load students for selection"""
        try:
            students = self.db_manager.get_all_students()
            student_list = [f"{student['roll_number']} - {student['full_name']}" for student in students]
            self.student_combo['values'] = student_list

            if student_list:
                self.student_combo.current(0)

        except Exception as e:
            self.logger.error(f"Failed to load students: {str(e)}")

    def set_today(self):
        """Set date to today"""
        today = date.today().strftime("%Y-%m-%d")
        self.start_date_var.set(today)
        self.end_date_var.set(today)

    def set_yesterday(self):
        """Set date to yesterday"""
        yesterday = (date.today() - timedelta(days=1)).strftime("%Y-%m-%d")
        self.start_date_var.set(yesterday)
        self.end_date_var.set(yesterday)

    def generate_report(self):
        """Generate report based on selected filters"""
        try:
            report_type = self.report_type_var.get()
            start_date_str = self.start_date_var.get()
            end_date_str = self.end_date_var.get()

            # Parse dates
            try:
                start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
                end_date = datetime.strptime(end_date_str, "%Y-%m-%d").date()
            except ValueError:
                UIHelper.show_error_message("Invalid Date", "Please enter valid dates in YYYY-MM-DD format")
                return

            # Validate date range
            if start_date > end_date:
                UIHelper.show_error_message("Invalid Date Range", "Start date must be before or equal to end date")
                return

            # Generate report based on type
            if report_type == "student":
                self.generate_student_report(start_date, end_date)
            else:
                self.generate_attendance_report(start_date, end_date, report_type)

            log_system_event("REPORT", f"Generated {report_type} report for {start_date} to {end_date}")

        except Exception as e:
            self.logger.error(f"Report generation error: {str(e)}")
            UIHelper.show_error_message("Report Error", f"Failed to generate report: {str(e)}")

    def generate_attendance_report(self, start_date, end_date, report_type):
        """Generate attendance report"""
        try:
            # Get attendance data
            if start_date == end_date:
                # Single day report
                attendance_records = self.db_manager.get_attendance_by_date(start_date)
                df = pd.DataFrame([dict(record) for record in attendance_records]) if attendance_records else pd.DataFrame()

                if not df.empty:
                    df['attendance_time'] = pd.to_datetime(df['attendance_time']).dt.strftime('%H:%M:%S')
                    df = df[['roll_number', 'full_name', 'class_name', 'section', 'attendance_time', 'status']]
                    df.columns = ['Roll Number', 'Student Name', 'Class', 'Section', 'Time', 'Status']

            else:
                # Multi-day report - get all attendance in range
                attendance_records = []
                current_date = start_date

                while current_date <= end_date:
                    daily_records = self.db_manager.get_attendance_by_date(current_date)
                    attendance_records.extend(daily_records)
                    current_date += timedelta(days=1)

                df = pd.DataFrame([dict(record) for record in attendance_records]) if attendance_records else pd.DataFrame()

                if not df.empty:
                    df['attendance_date'] = pd.to_datetime(df['attendance_time']).dt.strftime('%Y-%m-%d')
                    df['attendance_time'] = pd.to_datetime(df['attendance_time']).dt.strftime('%H:%M:%S')
                    df = df[['attendance_date', 'roll_number', 'full_name', 'class_name', 'attendance_time', 'status']]
                    df.columns = ['Date', 'Roll Number', 'Student Name', 'Class', 'Time', 'Status']

            # Store current report data
            self.current_report_data = df

            # Update display
            self.display_report(df, f"{report_type.title()} Attendance Report", start_date, end_date)

            # Update statistics
            self.update_statistics(df, start_date, end_date)

        except Exception as e:
            self.logger.error(f"Attendance report generation error: {str(e)}")
            raise

    def generate_student_report(self, start_date, end_date):
        """Generate student-specific report"""
        try:
            # Get selected student
            student_selection = self.student_var.get()
            if not student_selection:
                UIHelper.show_error_message("No Student Selected", "Please select a student")
                return

            # Extract roll number
            roll_number = student_selection.split(" - ")[0]
            student = self.db_manager.get_student_by_roll_number(roll_number)

            if not student:
                UIHelper.show_error_message("Student Not Found", "Selected student not found")
                return

            # Get student attendance
            attendance_records = self.db_manager.get_attendance_by_student(
                student['id'], start_date, end_date
            )

            df = pd.DataFrame([dict(record) for record in attendance_records]) if attendance_records else pd.DataFrame()

            if not df.empty:
                df['attendance_date'] = pd.to_datetime(df['attendance_date']).dt.strftime('%Y-%m-%d')
                df['attendance_time'] = pd.to_datetime(df['attendance_time']).dt.strftime('%H:%M:%S')
                df = df[['attendance_date', 'attendance_time', 'status']]
                df.columns = ['Date', 'Time', 'Status']

            # Store current report data
            self.current_report_data = df

            # Update display
            report_title = f"Attendance Report - {student['full_name']} ({roll_number})"
            self.display_report(df, report_title, start_date, end_date)

            # Update statistics
            self.update_student_statistics(df, start_date, end_date, student)

        except Exception as e:
            self.logger.error(f"Student report generation error: {str(e)}")
            raise

    def display_report(self, df, title, start_date, end_date):
        """Display report in treeview"""
        try:
            # Update title
            self.report_title_label.config(text=title)

            # Update info
            date_range = f"{start_date}" if start_date == end_date else f"{start_date} to {end_date}"
            record_count = len(df) if not df.empty else 0
            self.report_info_label.config(text=f"Date Range: {date_range} | Records: {record_count}")

            # Clear existing data
            for item in self.report_tree.get_children():
                self.report_tree.delete(item)

            if df.empty:
                # Show no data message
                self.report_tree['columns'] = ('Message',)
                self.report_tree.heading('Message', text='No Data')
                self.report_tree.column('Message', width=400)
                self.report_tree.insert('', tk.END, values=('No attendance records found for the selected criteria',))
                return

            # Configure columns
            columns = list(df.columns)
            self.report_tree['columns'] = columns

            # Set column headings and widths
            for col in columns:
                self.report_tree.heading(col, text=col)
                self.report_tree.column(col, width=120)

            # Insert data
            for _, row in df.iterrows():
                self.report_tree.insert('', tk.END, values=list(row))

        except Exception as e:
            self.logger.error(f"Report display error: {str(e)}")
            UIHelper.show_error_message("Display Error", f"Failed to display report: {str(e)}")

    def update_statistics(self, df, start_date, end_date):
        """Update statistics for attendance report"""
        try:
            if df.empty:
                self.stats_label.config(text="No data available")
                return

            total_records = len(df)
            unique_students = df['Student Name'].nunique() if 'Student Name' in df.columns else 0

            # Calculate date range
            days_count = (end_date - start_date).days + 1

            stats_text = f"""Total Records: {total_records}
Unique Students: {unique_students}
Date Range: {days_count} day(s)
Average per Day: {total_records / days_count:.1f}"""

            self.stats_label.config(text=stats_text)

        except Exception as e:
            self.logger.error(f"Statistics update error: {str(e)}")

    def update_student_statistics(self, df, start_date, end_date, student):
        """Update statistics for student report"""
        try:
            days_count = (end_date - start_date).days + 1
            present_days = len(df) if not df.empty else 0
            attendance_rate = (present_days / days_count) * 100 if days_count > 0 else 0

            stats_text = f"""Student: {student['full_name']}
Roll Number: {student['roll_number']}
Class: {student['class_name']}
Total Days: {days_count}
Present Days: {present_days}
Attendance Rate: {attendance_rate:.1f}%"""

            self.stats_label.config(text=stats_text)

        except Exception as e:
            self.logger.error(f"Student statistics update error: {str(e)}")

    def load_default_report(self):
        """Load default daily report for today"""
        try:
            today = date.today()
            self.generate_attendance_report(today, today, "daily")
        except Exception as e:
            self.logger.error(f"Failed to load default report: {str(e)}")

    def refresh_report(self):
        """Refresh current report"""
        self.generate_report()

    def export_report(self):
        """Export current report to CSV"""
        try:
            if self.current_report_data is None or self.current_report_data.empty:
                UIHelper.show_info_message("No Data", "No report data to export")
                return

            # Generate filename
            report_type = self.report_type_var.get()
            date_str = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{report_type}_report_{date_str}.csv"

            # Export
            file_path = self.report_generator.export_report_to_csv(self.current_report_data, filename)

            if file_path:
                UIHelper.show_info_message(
                    "Export Successful",
                    f"Report exported successfully!\nFile: {file_path}"
                )
                log_system_event("EXPORT", f"Report exported to {file_path}")
            else:
                UIHelper.show_error_message("Export Failed", "Failed to export report")

        except Exception as e:
            self.logger.error(f"Export error: {str(e)}")
            UIHelper.show_error_message("Export Error", f"Export failed: {str(e)}")

    def print_report(self):
        """Print current report"""
        UIHelper.show_info_message("Print", "Print functionality would be implemented here.\nFor now, please export to CSV and print from your preferred application.")

    def on_close(self):
        """Handle window close"""
        self.window.destroy()
        log_system_event("WINDOW_CLOSE", "Reports window closed")
