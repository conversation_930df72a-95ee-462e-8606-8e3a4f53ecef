"""
Student Registration Window for Face Recognition Attendance System
Handles student registration with face capture and encoding
"""

import tkinter as tk
from tkinter import ttk, messagebox
import cv2
from PIL import Image, ImageTk
import threading
import time
import config
from utils.database_manager import DatabaseManager
from utils.face_encoder import FaceEncoder
from utils.face_recognizer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from utils.helper import CameraManager, DataValidator, UIHelper, ImageProcessor
from utils.logger import setup_logger, log_system_event

class StudentRegistrationWindow:
    """Student registration window with face capture"""
    
    def __init__(self, parent):
        self.parent = parent
        self.db_manager = DatabaseManager()
        self.face_encoder = FaceEncoder()
        self.face_recognizer = FaceRecognizer()
        self.camera_manager = CameraManager()
        self.logger = setup_logger("StudentRegistration")
        
        # Registration state
        self.is_camera_active = False
        self.captured_face_encoding = None
        self.captured_face_image = None
        self.camera_thread = None
        
        # Create registration window
        self.window = tk.Toplevel(parent)
        self.window.title("Student Registration")
        self.window.geometry("900x700")
        self.window.configure(bg=config.BACKGROUND_COLOR)
        
        # Center window
        UIHelper.center_window(self.window, 900, 700)
        
        # Make window modal
        self.window.transient(parent)
        self.window.grab_set()
        
        # Setup UI
        self.setup_ui()
        
        # Handle window close
        self.window.protocol("WM_DELETE_WINDOW", self.on_close)
    
    def setup_ui(self):
        """Setup the registration UI"""
        # Main container
        main_frame = tk.Frame(self.window, bg=config.BACKGROUND_COLOR)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Title
        title_label = tk.Label(
            main_frame,
            text="Student Registration",
            font=('Arial', 18, 'bold'),
            fg=config.THEME_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        title_label.pack(pady=(0, 20))
        
        # Create two-column layout
        content_frame = tk.Frame(main_frame, bg=config.BACKGROUND_COLOR)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # Left column - Student Information
        self.create_student_info_panel(content_frame)
        
        # Right column - Face Capture
        self.create_face_capture_panel(content_frame)
        
        # Bottom buttons
        self.create_action_buttons(main_frame)
    
    def create_student_info_panel(self, parent):
        """Create student information input panel"""
        # Left frame
        left_frame = tk.Frame(parent, bg=config.BACKGROUND_COLOR)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # Student info title
        info_title = tk.Label(
            left_frame,
            text="Student Information",
            font=('Arial', 14, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        info_title.pack(anchor=tk.W, pady=(0, 15))
        
        # Form fields
        self.create_form_field(left_frame, "Roll Number:", "roll_number", required=True)
        self.create_form_field(left_frame, "Full Name:", "full_name", required=True)
        self.create_form_field(left_frame, "Class:", "class_name", required=True)
        self.create_form_field(left_frame, "Section:", "section")
        self.create_form_field(left_frame, "Email:", "email")
        self.create_form_field(left_frame, "Phone:", "phone")
        self.create_form_field(left_frame, "Address:", "address", multiline=True)
        
        # Validation status
        self.validation_label = tk.Label(
            left_frame,
            text="",
            font=('Arial', 10),
            bg=config.BACKGROUND_COLOR,
            wraplength=350
        )
        self.validation_label.pack(anchor=tk.W, pady=(10, 0))
    
    def create_form_field(self, parent, label_text, field_name, required=False, multiline=False):
        """Create a form field"""
        # Field frame
        field_frame = tk.Frame(parent, bg=config.BACKGROUND_COLOR)
        field_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Label
        label = tk.Label(
            field_frame,
            text=label_text + (" *" if required else ""),
            font=('Arial', 11, 'bold' if required else 'normal'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        label.pack(anchor=tk.W, pady=(0, 5))
        
        # Entry widget
        if multiline:
            entry = tk.Text(
                field_frame,
                font=('Arial', 10),
                height=3,
                relief=tk.FLAT,
                bd=1,
                highlightthickness=1,
                highlightcolor=config.THEME_COLOR
            )
        else:
            entry = tk.Entry(
                field_frame,
                font=('Arial', 10),
                relief=tk.FLAT,
                bd=1,
                highlightthickness=1,
                highlightcolor=config.THEME_COLOR
            )
        
        entry.pack(fill=tk.X, ipady=5)
        
        # Store reference
        setattr(self, f"{field_name}_entry", entry)
    
    def create_face_capture_panel(self, parent):
        """Create face capture panel"""
        # Right frame
        right_frame = tk.Frame(parent, bg=config.BACKGROUND_COLOR)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))
        
        # Face capture title
        capture_title = tk.Label(
            right_frame,
            text="Face Capture",
            font=('Arial', 14, 'bold'),
            fg=config.TEXT_COLOR,
            bg=config.BACKGROUND_COLOR
        )
        capture_title.pack(anchor=tk.W, pady=(0, 15))
        
        # Camera frame
        self.camera_frame = tk.Frame(
            right_frame,
            bg='black',
            width=400,
            height=300,
            relief=tk.SUNKEN,
            bd=2
        )
        self.camera_frame.pack(pady=(0, 10))
        self.camera_frame.pack_propagate(False)
        
        # Camera display label
        self.camera_label = tk.Label(
            self.camera_frame,
            text="Camera Preview\nClick 'Start Camera' to begin",
            font=('Arial', 12),
            fg='white',
            bg='black',
            justify=tk.CENTER
        )
        self.camera_label.pack(expand=True)
        
        # Camera controls
        camera_controls = tk.Frame(right_frame, bg=config.BACKGROUND_COLOR)
        camera_controls.pack(fill=tk.X, pady=(0, 10))
        
        self.start_camera_btn = tk.Button(
            camera_controls,
            text="Start Camera",
            font=('Arial', 10, 'bold'),
            bg=config.BUTTON_COLOR,
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.toggle_camera
        )
        self.start_camera_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.capture_btn = tk.Button(
            camera_controls,
            text="Capture Face",
            font=('Arial', 10, 'bold'),
            bg='green',
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            state=tk.DISABLED,
            command=self.capture_face
        )
        self.capture_btn.pack(side=tk.LEFT, padx=5)
        
        # Face status
        self.face_status_label = tk.Label(
            right_frame,
            text="No face captured",
            font=('Arial', 11),
            fg='red',
            bg=config.BACKGROUND_COLOR
        )
        self.face_status_label.pack(pady=(10, 0))
        
        # Face quality info
        self.quality_label = tk.Label(
            right_frame,
            text="",
            font=('Arial', 10),
            fg='gray',
            bg=config.BACKGROUND_COLOR,
            wraplength=350,
            justify=tk.LEFT
        )
        self.quality_label.pack(pady=(5, 0))
    
    def create_action_buttons(self, parent):
        """Create action buttons"""
        button_frame = tk.Frame(parent, bg=config.BACKGROUND_COLOR)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        # Register button
        self.register_btn = tk.Button(
            button_frame,
            text="Register Student",
            font=('Arial', 12, 'bold'),
            bg=config.THEME_COLOR,
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            state=tk.DISABLED,
            command=self.register_student
        )
        self.register_btn.pack(side=tk.LEFT, padx=(0, 10), ipady=8, ipadx=20)
        
        # Clear button
        clear_btn = tk.Button(
            button_frame,
            text="Clear Form",
            font=('Arial', 12),
            bg='orange',
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.clear_form
        )
        clear_btn.pack(side=tk.LEFT, padx=10, ipady=8, ipadx=20)
        
        # Close button
        close_btn = tk.Button(
            button_frame,
            text="Close",
            font=('Arial', 12),
            bg='gray',
            fg='white',
            relief=tk.FLAT,
            cursor='hand2',
            command=self.on_close
        )
        close_btn.pack(side=tk.RIGHT, ipady=8, ipadx=20)
    
    def toggle_camera(self):
        """Toggle camera on/off"""
        if not self.is_camera_active:
            self.start_camera()
        else:
            self.stop_camera()
    
    def start_camera(self):
        """Start camera preview"""
        try:
            if self.camera_manager.initialize_camera():
                self.is_camera_active = True
                self.start_camera_btn.config(text="Stop Camera", bg='red')
                self.capture_btn.config(state=tk.NORMAL)
                
                # Start camera thread
                self.camera_thread = threading.Thread(target=self.camera_loop, daemon=True)
                self.camera_thread.start()
                
                log_system_event("CAMERA", "Camera started for registration")
            else:
                UIHelper.show_error_message("Camera Error", "Failed to initialize camera")
                
        except Exception as e:
            self.logger.error(f"Camera start error: {str(e)}")
            UIHelper.show_error_message("Camera Error", f"Camera error: {str(e)}")
    
    def stop_camera(self):
        """Stop camera preview"""
        self.is_camera_active = False
        self.camera_manager.release_camera()
        self.start_camera_btn.config(text="Start Camera", bg=config.BUTTON_COLOR)
        self.capture_btn.config(state=tk.DISABLED)
        
        # Reset camera display
        self.camera_label.config(
            image='',
            text="Camera Preview\nClick 'Start Camera' to begin"
        )
        
        log_system_event("CAMERA", "Camera stopped")
    
    def camera_loop(self):
        """Camera preview loop"""
        while self.is_camera_active:
            try:
                frame = self.camera_manager.read_frame()
                if frame is not None:
                    # Resize frame for display
                    display_frame = ImageProcessor.resize_image_for_display(frame, 400, 300)
                    
                    # Convert to PhotoImage
                    photo = ImageProcessor.convert_cv2_to_tkinter(display_frame)
                    
                    # Update display
                    self.camera_label.config(image=photo, text='')
                    self.camera_label.image = photo  # Keep reference
                
                time.sleep(0.033)  # ~30 FPS
                
            except Exception as e:
                self.logger.error(f"Camera loop error: {str(e)}")
                break
    
    def capture_face(self):
        """Capture face from current frame"""
        try:
            frame = self.camera_manager.read_frame()
            if frame is None:
                UIHelper.show_error_message("Capture Error", "No camera frame available")
                return
            
            # Detect and encode face
            face_encoding, face_location = self.face_encoder.encode_face_from_webcam_frame(frame)
            
            if face_encoding is None:
                UIHelper.show_error_message("Face Detection", "No face detected in frame")
                return
            
            # Validate face quality
            is_valid, quality_score, issues = self.face_encoder.validate_face_quality(
                face_encoding, face_location, frame.shape
            )
            
            if not is_valid:
                issue_text = "\n".join(issues)
                UIHelper.show_warning_message(
                    "Face Quality Warning", 
                    f"Face quality issues detected:\n{issue_text}\n\nQuality Score: {quality_score}%"
                )
            
            # Check for duplicate faces
            is_unique, similar_faces = self.face_recognizer.validate_face_encoding_uniqueness(face_encoding)
            
            if not is_unique:
                similar_names = [face['student_info']['full_name'] for face in similar_faces[:3]]
                UIHelper.show_warning_message(
                    "Similar Face Detected",
                    f"This face is similar to existing students:\n{', '.join(similar_names)}\n\nProceed with caution."
                )
            
            # Store captured data
            self.captured_face_encoding = face_encoding
            self.captured_face_image = frame.copy()
            
            # Update UI
            self.face_status_label.config(text="Face captured successfully!", fg='green')
            self.quality_label.config(
                text=f"Quality Score: {quality_score}%\nIssues: {', '.join(issues) if issues else 'None'}"
            )
            
            # Enable register button if form is valid
            self.validate_form()
            
            log_system_event("FACE_CAPTURE", f"Face captured with quality score: {quality_score}%")
            
        except Exception as e:
            self.logger.error(f"Face capture error: {str(e)}")
            UIHelper.show_error_message("Capture Error", f"Face capture failed: {str(e)}")
    
    def validate_form(self):
        """Validate form data"""
        errors = []
        
        # Get form data
        roll_number = self.roll_number_entry.get().strip()
        full_name = self.full_name_entry.get().strip()
        class_name = self.class_name_entry.get().strip()
        email = self.email_entry.get().strip()
        phone = self.phone_entry.get().strip()
        
        # Validate required fields
        if not roll_number:
            errors.append("Roll number is required")
        else:
            valid, msg = DataValidator.validate_roll_number(roll_number)
            if not valid:
                errors.append(f"Roll number: {msg}")
        
        if not full_name:
            errors.append("Full name is required")
        else:
            valid, msg = DataValidator.validate_student_name(full_name)
            if not valid:
                errors.append(f"Full name: {msg}")
        
        if not class_name:
            errors.append("Class is required")
        
        # Validate optional fields
        if email:
            valid, msg = DataValidator.validate_email(email)
            if not valid:
                errors.append(f"Email: {msg}")
        
        if phone:
            valid, msg = DataValidator.validate_phone(phone)
            if not valid:
                errors.append(f"Phone: {msg}")
        
        # Check if face is captured
        if self.captured_face_encoding is None:
            errors.append("Face capture is required")
        
        # Update validation display
        if errors:
            self.validation_label.config(text=f"Validation errors:\n• " + "\n• ".join(errors), fg='red')
            self.register_btn.config(state=tk.DISABLED)
        else:
            self.validation_label.config(text="Form validation passed ✓", fg='green')
            self.register_btn.config(state=tk.NORMAL)
        
        return len(errors) == 0

    def register_student(self):
        """Register the student"""
        try:
            # Validate form one more time
            if not self.validate_form():
                return

            # Get form data
            student_data = self.get_form_data()

            # Check for duplicate roll number
            existing_student = self.db_manager.get_student_by_roll_number(student_data['roll_number'])
            if existing_student:
                UIHelper.show_error_message(
                    "Duplicate Roll Number",
                    f"A student with roll number '{student_data['roll_number']}' already exists."
                )
                return

            # Disable register button during processing
            self.register_btn.config(state=tk.DISABLED, text="Registering...")
            self.window.update()

            # Save face image
            face_image_path = self.face_encoder.save_face_image(
                self.captured_face_image,
                self.get_face_location_from_encoding(),
                student_data['roll_number']
            )

            if face_image_path:
                student_data['photo_path'] = face_image_path

            # Add student to database
            student_id = self.db_manager.add_student(student_data, self.captured_face_encoding)

            if student_id:
                # Reload face recognizer with new data
                self.face_recognizer.reload_known_faces()

                # Show success message
                UIHelper.show_info_message(
                    "Registration Successful",
                    f"Student '{student_data['full_name']}' has been registered successfully!\n"
                    f"Student ID: {student_id}"
                )

                log_system_event(
                    "STUDENT_REGISTRATION",
                    f"Student registered: {student_data['full_name']} ({student_data['roll_number']})"
                )

                # Clear form for next registration
                self.clear_form()

            else:
                UIHelper.show_error_message("Registration Failed", "Failed to register student in database")

        except Exception as e:
            self.logger.error(f"Student registration error: {str(e)}")
            UIHelper.show_error_message("Registration Error", f"Registration failed: {str(e)}")

        finally:
            # Re-enable register button
            self.register_btn.config(state=tk.NORMAL, text="Register Student")

    def get_form_data(self):
        """Get form data as dictionary"""
        # Get text from Text widget (address)
        address_text = self.address_entry.get("1.0", tk.END).strip()

        return {
            'roll_number': self.roll_number_entry.get().strip(),
            'full_name': self.full_name_entry.get().strip(),
            'class_name': self.class_name_entry.get().strip(),
            'section': self.section_entry.get().strip(),
            'email': self.email_entry.get().strip(),
            'phone': self.phone_entry.get().strip(),
            'address': address_text
        }

    def get_face_location_from_encoding(self):
        """Get face location for the captured encoding (simplified)"""
        # This is a simplified version - in a real implementation,
        # you would store the face location when capturing
        frame_height, frame_width = self.captured_face_image.shape[:2]

        # Return a centered face location estimate
        center_x = frame_width // 2
        center_y = frame_height // 2
        face_size = min(frame_width, frame_height) // 4

        top = center_y - face_size
        bottom = center_y + face_size
        left = center_x - face_size
        right = center_x + face_size

        return (top, right, bottom, left)

    def clear_form(self):
        """Clear all form fields"""
        # Clear text entries
        for field in ['roll_number', 'full_name', 'class_name', 'section', 'email', 'phone']:
            entry = getattr(self, f"{field}_entry")
            entry.delete(0, tk.END)

        # Clear text widget (address)
        self.address_entry.delete("1.0", tk.END)

        # Clear face data
        self.captured_face_encoding = None
        self.captured_face_image = None

        # Reset UI
        self.face_status_label.config(text="No face captured", fg='red')
        self.quality_label.config(text="")
        self.validation_label.config(text="")
        self.register_btn.config(state=tk.DISABLED)

        # Focus on first field
        self.roll_number_entry.focus()

    def on_close(self):
        """Handle window close"""
        # Stop camera if active
        if self.is_camera_active:
            self.stop_camera()

        # Close window
        self.window.destroy()

        log_system_event("WINDOW_CLOSE", "Student Registration window closed")
