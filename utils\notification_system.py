"""
Advanced Notification System for Face Recognition Attendance System
Handles email notifications, alerts, and system notifications
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
import threading
import queue
from datetime import datetime, timedelta
from collections import defaultdict
import config
from utils.logger import setup_logger

class NotificationSystem:
    """Advanced notification system with email and alert capabilities"""
    
    def __init__(self):
        self.logger = setup_logger("NotificationSystem")
        
        # Email configuration
        self.smtp_server = config.EMAIL_SMTP_SERVER
        self.smtp_port = config.EMAIL_SMTP_PORT
        self.use_tls = config.EMAIL_USE_TLS
        self.email_enabled = config.EMAIL_ENABLED
        
        # Notification queue
        self.notification_queue = queue.Queue()
        self.is_processing = False
        self.processor_thread = None
        
        # Rate limiting
        self.rate_limits = defaultdict(list)  # email -> [timestamps]
        self.max_emails_per_hour = 10
        
        # Notification templates
        self.templates = {
            'attendance_marked': {
                'subject': 'Attendance Marked - {student_name}',
                'body': '''
Dear Parent/Guardian,

This is to inform you that attendance has been marked for {student_name} (Roll No: {roll_number}) 
in class {class_name} on {date} at {time}.

Confidence Score: {confidence}%

Best regards,
Face Recognition Attendance System
'''
            },
            'daily_report': {
                'subject': 'Daily Attendance Report - {date}',
                'body': '''
Daily Attendance Report for {date}

Total Students: {total_students}
Present Students: {present_students}
Attendance Rate: {attendance_rate}%

Detailed report is attached.

Best regards,
Face Recognition Attendance System
'''
            },
            'system_alert': {
                'subject': 'System Alert - {alert_type}',
                'body': '''
System Alert Notification

Alert Type: {alert_type}
Severity: {severity}
Time: {timestamp}
Message: {message}

Please check the system for more details.

Best regards,
Face Recognition Attendance System
'''
            },
            'low_attendance': {
                'subject': 'Low Attendance Alert - {student_name}',
                'body': '''
Dear Parent/Guardian,

This is to inform you that {student_name} (Roll No: {roll_number}) has low attendance.

Current Attendance Rate: {attendance_rate}%
Days Present: {days_present} out of {total_days}

Please ensure regular attendance.

Best regards,
Face Recognition Attendance System
'''
            }
        }
        
        # Notification history
        self.notification_history = []
        
    def start_processing(self):
        """Start notification processing"""
        if not self.is_processing:
            self.is_processing = True
            self.processor_thread = threading.Thread(target=self._process_notifications, daemon=True)
            self.processor_thread.start()
            self.logger.info("Notification processing started")
    
    def stop_processing(self):
        """Stop notification processing"""
        self.is_processing = False
        if self.processor_thread:
            self.processor_thread.join(timeout=5)
        self.logger.info("Notification processing stopped")
    
    def _process_notifications(self):
        """Process notification queue"""
        while self.is_processing:
            try:
                # Get notification from queue (blocking with timeout)
                notification = self.notification_queue.get(timeout=5)
                
                # Process the notification
                self._send_notification(notification)
                
                # Mark task as done
                self.notification_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"Notification processing error: {e}")
    
    def send_attendance_notification(self, student_info, attendance_data, parent_email=None):
        """Send attendance marked notification"""
        try:
            if not self.email_enabled:
                return False
            
            # Use student email if parent email not provided
            email = parent_email or student_info.get('email')
            if not email:
                return False
            
            # Check rate limiting
            if not self._check_rate_limit(email):
                self.logger.warning(f"Rate limit exceeded for {email}")
                return False
            
            # Prepare notification data
            notification_data = {
                'type': 'attendance_marked',
                'recipient': email,
                'data': {
                    'student_name': student_info['full_name'],
                    'roll_number': student_info['roll_number'],
                    'class_name': student_info['class_name'],
                    'date': attendance_data['date'],
                    'time': attendance_data['time'],
                    'confidence': attendance_data.get('confidence', 0)
                }
            }
            
            # Add to queue
            self.notification_queue.put(notification_data)
            return True
            
        except Exception as e:
            self.logger.error(f"Attendance notification preparation failed: {e}")
            return False
    
    def send_daily_report_notification(self, report_data, recipient_emails, attachment_path=None):
        """Send daily attendance report notification"""
        try:
            if not self.email_enabled or not recipient_emails:
                return False
            
            for email in recipient_emails:
                # Check rate limiting
                if not self._check_rate_limit(email):
                    continue
                
                # Prepare notification data
                notification_data = {
                    'type': 'daily_report',
                    'recipient': email,
                    'data': report_data,
                    'attachment': attachment_path
                }
                
                # Add to queue
                self.notification_queue.put(notification_data)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Daily report notification preparation failed: {e}")
            return False
    
    def send_system_alert(self, alert_type, severity, message, admin_emails):
        """Send system alert notification"""
        try:
            if not self.email_enabled or not admin_emails:
                return False
            
            for email in admin_emails:
                # Prepare notification data
                notification_data = {
                    'type': 'system_alert',
                    'recipient': email,
                    'data': {
                        'alert_type': alert_type,
                        'severity': severity,
                        'message': message,
                        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }
                }
                
                # Add to queue
                self.notification_queue.put(notification_data)
            
            return True
            
        except Exception as e:
            self.logger.error(f"System alert notification preparation failed: {e}")
            return False
    
    def send_low_attendance_alert(self, student_info, attendance_stats, parent_email=None):
        """Send low attendance alert"""
        try:
            if not self.email_enabled:
                return False
            
            # Use student email if parent email not provided
            email = parent_email or student_info.get('email')
            if not email:
                return False
            
            # Check if attendance is actually low
            attendance_rate = attendance_stats.get('attendance_rate', 100)
            if attendance_rate >= 75:  # Don't send if attendance is good
                return False
            
            # Check rate limiting
            if not self._check_rate_limit(email):
                return False
            
            # Prepare notification data
            notification_data = {
                'type': 'low_attendance',
                'recipient': email,
                'data': {
                    'student_name': student_info['full_name'],
                    'roll_number': student_info['roll_number'],
                    'attendance_rate': attendance_rate,
                    'days_present': attendance_stats.get('days_present', 0),
                    'total_days': attendance_stats.get('total_days', 0)
                }
            }
            
            # Add to queue
            self.notification_queue.put(notification_data)
            return True
            
        except Exception as e:
            self.logger.error(f"Low attendance alert preparation failed: {e}")
            return False
    
    def _send_notification(self, notification_data):
        """Send individual notification"""
        try:
            notification_type = notification_data['type']
            recipient = notification_data['recipient']
            data = notification_data['data']
            attachment = notification_data.get('attachment')
            
            # Get template
            template = self.templates.get(notification_type)
            if not template:
                self.logger.error(f"Unknown notification type: {notification_type}")
                return False
            
            # Format subject and body
            subject = template['subject'].format(**data)
            body = template['body'].format(**data)
            
            # Send email
            success = self._send_email(recipient, subject, body, attachment)
            
            # Record in history
            self.notification_history.append({
                'timestamp': datetime.now(),
                'type': notification_type,
                'recipient': recipient,
                'success': success
            })
            
            # Update rate limiting
            if success:
                self.rate_limits[recipient].append(datetime.now())
            
            return success
            
        except Exception as e:
            self.logger.error(f"Notification sending failed: {e}")
            return False
    
    def _send_email(self, recipient, subject, body, attachment_path=None):
        """Send email using SMTP"""
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = "<EMAIL>"  # Configure this
            msg['To'] = recipient
            msg['Subject'] = subject
            
            # Add body
            msg.attach(MIMEText(body, 'plain'))
            
            # Add attachment if provided
            if attachment_path and os.path.exists(attachment_path):
                with open(attachment_path, "rb") as attachment:
                    part = MIMEBase('application', 'octet-stream')
                    part.set_payload(attachment.read())
                
                encoders.encode_base64(part)
                part.add_header(
                    'Content-Disposition',
                    f'attachment; filename= {os.path.basename(attachment_path)}'
                )
                msg.attach(part)
            
            # Create SMTP session
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            
            if self.use_tls:
                server.starttls()
            
            # Login (configure credentials)
            # server.login("<EMAIL>", "your_password")
            
            # Send email
            text = msg.as_string()
            server.sendmail("<EMAIL>", recipient, text)
            server.quit()
            
            self.logger.info(f"Email sent successfully to {recipient}")
            return True
            
        except Exception as e:
            self.logger.error(f"Email sending failed to {recipient}: {e}")
            return False
    
    def _check_rate_limit(self, email):
        """Check if email is within rate limits"""
        try:
            current_time = datetime.now()
            one_hour_ago = current_time - timedelta(hours=1)
            
            # Clean old timestamps
            self.rate_limits[email] = [
                timestamp for timestamp in self.rate_limits[email]
                if timestamp > one_hour_ago
            ]
            
            # Check if under limit
            return len(self.rate_limits[email]) < self.max_emails_per_hour
            
        except Exception as e:
            self.logger.error(f"Rate limit check failed: {e}")
            return True  # Allow on error
    
    def get_notification_statistics(self):
        """Get notification statistics"""
        try:
            total_notifications = len(self.notification_history)
            successful_notifications = sum(1 for n in self.notification_history if n['success'])
            
            # Count by type
            type_counts = defaultdict(int)
            for notification in self.notification_history:
                type_counts[notification['type']] += 1
            
            # Recent notifications (last 24 hours)
            recent_cutoff = datetime.now() - timedelta(hours=24)
            recent_notifications = [
                n for n in self.notification_history
                if n['timestamp'] > recent_cutoff
            ]
            
            return {
                'total_notifications': total_notifications,
                'successful_notifications': successful_notifications,
                'success_rate': (successful_notifications / total_notifications * 100) if total_notifications > 0 else 0,
                'notifications_by_type': dict(type_counts),
                'recent_notifications_24h': len(recent_notifications),
                'queue_size': self.notification_queue.qsize(),
                'email_enabled': self.email_enabled
            }
            
        except Exception as e:
            self.logger.error(f"Notification statistics failed: {e}")
            return {}
    
    def configure_email_settings(self, smtp_server, smtp_port, use_tls=True, username=None, password=None):
        """Configure email settings"""
        try:
            self.smtp_server = smtp_server
            self.smtp_port = smtp_port
            self.use_tls = use_tls
            
            # Store credentials securely (in production, use proper credential management)
            if username and password:
                # This is a simplified approach - use proper credential storage in production
                pass
            
            self.logger.info("Email settings configured")
            return True
            
        except Exception as e:
            self.logger.error(f"Email configuration failed: {e}")
            return False
    
    def test_email_configuration(self, test_recipient):
        """Test email configuration"""
        try:
            test_notification = {
                'type': 'system_alert',
                'recipient': test_recipient,
                'data': {
                    'alert_type': 'TEST',
                    'severity': 'INFO',
                    'message': 'This is a test email to verify email configuration.',
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            }
            
            return self._send_notification(test_notification)
            
        except Exception as e:
            self.logger.error(f"Email test failed: {e}")
            return False
    
    def cleanup_old_history(self, days_to_keep=30):
        """Clean up old notification history"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            original_count = len(self.notification_history)
            self.notification_history = [
                n for n in self.notification_history
                if n['timestamp'] > cutoff_date
            ]
            
            cleaned_count = original_count - len(self.notification_history)
            if cleaned_count > 0:
                self.logger.info(f"Cleaned up {cleaned_count} old notification records")
            
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"Notification history cleanup failed: {e}")
            return 0

# Global notification system instance
notification_system = NotificationSystem()
