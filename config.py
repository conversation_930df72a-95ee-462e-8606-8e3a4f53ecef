"""
Configuration file for Face Recognition Attendance System
Contains all system settings and constants
"""

import os
from pathlib import Path

# Base directory
BASE_DIR = Path(__file__).parent

# Directory paths
DATABASE_DIR = BASE_DIR / "database"
FACES_DIR = BASE_DIR / "faces"
ATTENDANCE_DIR = BASE_DIR / "attendance"
GUI_DIR = BASE_DIR / "gui"
UTILS_DIR = BASE_DIR / "utils"

# Database settings
DATABASE_PATH = DATABASE_DIR / "attendance.db"

# Face recognition settings
FACE_ENCODINGS_PATH = DATABASE_DIR / "face_encodings.pkl"
TOLERANCE = 0.6  # Face recognition tolerance (lower = more strict)
MODEL = "hog"  # Face detection model: 'hog' or 'cnn'

# Camera settings
CAMERA_INDEX = 0  # Default camera index
FRAME_WIDTH = 640
FRAME_HEIGHT = 480
FPS = 30

# GUI settings
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
THEME_COLOR = "#2E86AB"
BACKGROUND_COLOR = "#F8F9FA"
BUTTON_COLOR = "#A23B72"
TEXT_COLOR = "#2D3436"

# Attendance settings
ATTENDANCE_TIME_FORMAT = "%Y-%m-%d %H:%M:%S"
ATTENDANCE_DATE_FORMAT = "%Y-%m-%d"
MIN_ATTENDANCE_INTERVAL = 300  # Minimum seconds between attendance marks (5 minutes)

# Email settings (for notifications)
SMTP_SERVER = "smtp.gmail.com"
SMTP_PORT = 587
EMAIL_ENABLED = False  # Set to True to enable email notifications

# File formats
SUPPORTED_IMAGE_FORMATS = ['.jpg', '.jpeg', '.png', '.bmp']
ATTENDANCE_EXPORT_FORMAT = "csv"  # csv or xlsx

# Logging settings
LOG_LEVEL = "INFO"
LOG_FILE = BASE_DIR / "system.log"

# Security settings
PASSWORD_MIN_LENGTH = 6
SESSION_TIMEOUT = 3600  # Session timeout in seconds (1 hour)

# System messages
MESSAGES = {
    "FACE_DETECTED": "Face detected successfully!",
    "FACE_NOT_DETECTED": "No face detected. Please position yourself properly.",
    "ATTENDANCE_MARKED": "Attendance marked successfully!",
    "DUPLICATE_ATTENDANCE": "Attendance already marked for today.",
    "UNKNOWN_FACE": "Face not recognized. Please register first.",
    "REGISTRATION_SUCCESS": "Student registered successfully!",
    "LOGIN_SUCCESS": "Login successful!",
    "LOGIN_FAILED": "Invalid credentials!",
    "DATABASE_ERROR": "Database error occurred.",
    "CAMERA_ERROR": "Camera not accessible."
}

# Create directories if they don't exist
for directory in [DATABASE_DIR, FACES_DIR, ATTENDANCE_DIR]:
    directory.mkdir(exist_ok=True)
