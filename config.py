"""
Configuration file for Face Recognition Attendance System
Contains all system settings and constants
"""

from pathlib import Path

# Base directory
BASE_DIR = Path(__file__).parent

# Directory paths
DATABASE_DIR = BASE_DIR / "database"
FACES_DIR = BASE_DIR / "faces"
ATTENDANCE_DIR = BASE_DIR / "attendance"
GUI_DIR = BASE_DIR / "gui"
UTILS_DIR = BASE_DIR / "utils"

# Database settings
DATABASE_PATH = DATABASE_DIR / "attendance.db"

# Face recognition settings
FACE_ENCODINGS_PATH = DATABASE_DIR / "face_encodings.pkl"
TOLERANCE = 0.6  # Face recognition tolerance (lower = more strict)
MODEL = "hog"  # Face detection model: 'hog' or 'cnn'

# Camera settings
CAMERA_INDEX = 0  # Default camera index
FRAME_WIDTH = 640
FRAME_HEIGHT = 480
FPS = 30

# GUI settings
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
THEME_COLOR = "#2E86AB"
BACKGROUND_COLOR = "#F8F9FA"
BUTTON_COLOR = "#A23B72"
TEXT_COLOR = "#2D3436"

# Attendance settings
ATTENDANCE_TIME_FORMAT = "%Y-%m-%d %H:%M:%S"
ATTENDANCE_DATE_FORMAT = "%Y-%m-%d"
MIN_ATTENDANCE_INTERVAL = 300  # Minimum seconds between attendance marks (5 minutes)

# Email settings (for notifications)
SMTP_SERVER = "smtp.gmail.com"
SMTP_PORT = 587
EMAIL_ENABLED = False  # Set to True to enable email notifications

# File formats
SUPPORTED_IMAGE_FORMATS = ['.jpg', '.jpeg', '.png', '.bmp']
ATTENDANCE_EXPORT_FORMAT = "csv"  # csv or xlsx

# Logging settings
LOG_LEVEL = "INFO"
LOG_FILE = BASE_DIR / "system.log"

# Security settings
PASSWORD_MIN_LENGTH = 6
SESSION_TIMEOUT = 3600  # Session timeout in seconds (1 hour)

# System messages
MESSAGES = {
    "FACE_DETECTED": "Face detected successfully!",
    "FACE_NOT_DETECTED": "No face detected. Please position yourself properly.",
    "ATTENDANCE_MARKED": "Attendance marked successfully!",
    "DUPLICATE_ATTENDANCE": "Attendance already marked for today.",
    "UNKNOWN_FACE": "Face not recognized. Please register first.",
    "REGISTRATION_SUCCESS": "Student registered successfully!",
    "LOGIN_SUCCESS": "Login successful!",
    "LOGIN_FAILED": "Invalid credentials!",
    "DATABASE_ERROR": "Database error occurred.",
    "CAMERA_ERROR": "Camera not accessible."
}

# Advanced face recognition settings
FACE_DETECTION_SCALE_FACTOR = 1.1
FACE_DETECTION_MIN_NEIGHBORS = 5
FACE_DETECTION_MIN_SIZE = (30, 30)
FACE_RECOGNITION_BATCH_SIZE = 10

# Performance settings
MAX_CONCURRENT_RECOGNITIONS = 3
FRAME_SKIP_COUNT = 2  # Process every nth frame for performance
IMAGE_RESIZE_FACTOR = 0.5  # Resize factor for faster processing

# Advanced attendance settings
ATTENDANCE_CONFIDENCE_THRESHOLD = 0.6
DUPLICATE_DETECTION_WINDOW = 300  # seconds
AUTO_BACKUP_INTERVAL = 3600  # seconds (1 hour)
SESSION_CLEANUP_INTERVAL = 86400  # seconds (24 hours)

# Notification settings
EMAIL_SMTP_SERVER = "smtp.gmail.com"
EMAIL_SMTP_PORT = 587
EMAIL_USE_TLS = True
NOTIFICATION_BATCH_SIZE = 50

# Report settings
REPORT_MAX_RECORDS = 10000
REPORT_CACHE_DURATION = 300  # seconds
EXPORT_FORMATS = ['csv', 'xlsx', 'pdf']

# Security settings
MAX_LOGIN_ATTEMPTS = 3
ACCOUNT_LOCKOUT_DURATION = 900  # seconds (15 minutes)
PASSWORD_COMPLEXITY_REQUIRED = True
SESSION_ENCRYPTION_KEY = "face_attendance_system_2025"

# Advanced logging
LOG_ROTATION_SIZE = 10 * 1024 * 1024  # 10MB
LOG_BACKUP_COUNT = 5
LOG_FORMAT_DETAILED = True

# Performance monitoring
ENABLE_PERFORMANCE_MONITORING = True
MEMORY_USAGE_THRESHOLD = 500  # MB
CPU_USAGE_THRESHOLD = 80  # percentage

# Backup settings
AUTO_BACKUP_ENABLED = True
BACKUP_RETENTION_DAYS = 30
BACKUP_COMPRESSION = True

# Advanced features flags
ENABLE_EMOTION_DETECTION = False
ENABLE_AGE_ESTIMATION = False
ENABLE_GENDER_CLASSIFICATION = False
ENABLE_MASK_DETECTION = True
ENABLE_LIVENESS_DETECTION = False

# API settings (for future mobile app integration)
API_ENABLED = False
API_PORT = 8080
API_HOST = "localhost"
API_AUTH_REQUIRED = True

# Multi-camera support
MULTI_CAMERA_ENABLED = False
CAMERA_INDICES = [0, 1, 2]  # Available camera indices

# Cloud integration settings
CLOUD_SYNC_ENABLED = False
CLOUD_PROVIDER = "none"  # aws, azure, gcp
CLOUD_BACKUP_INTERVAL = 86400  # seconds

# Create directories if they don't exist
for directory in [DATABASE_DIR, FACES_DIR, ATTENDANCE_DIR]:
    directory.mkdir(exist_ok=True)

# Create additional directories for advanced features
BACKUP_DIR = BASE_DIR / "backups"
REPORTS_DIR = BASE_DIR / "reports"
LOGS_DIR = BASE_DIR / "logs"
TEMP_DIR = BASE_DIR / "temp"

for directory in [BACKUP_DIR, REPORTS_DIR, LOGS_DIR, TEMP_DIR]:
    directory.mkdir(exist_ok=True)
