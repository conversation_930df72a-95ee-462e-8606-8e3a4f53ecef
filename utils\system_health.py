"""
System Health Monitor for Face Recognition Attendance System
Monitors system health, performance, and provides diagnostics
"""

import psutil
import time
import threading
from datetime import datetime, timedelta
from collections import deque
import config
from utils.logger import setup_logger

class SystemHealthMonitor:
    """Comprehensive system health monitoring"""
    
    def __init__(self):
        self.logger = setup_logger("SystemHealth")
        self.is_monitoring = False
        self.monitor_thread = None
        
        # Health metrics
        self.health_metrics = {
            'cpu_usage': deque(maxlen=60),  # Last 60 readings
            'memory_usage': deque(maxlen=60),
            'disk_usage': deque(maxlen=10),
            'network_io': deque(maxlen=30),
            'process_count': deque(maxlen=30),
            'system_load': deque(maxlen=30)
        }
        
        # Health status
        self.health_status = {
            'overall': 'unknown',
            'cpu': 'unknown',
            'memory': 'unknown',
            'disk': 'unknown',
            'network': 'unknown',
            'last_check': None
        }
        
        # Alerts
        self.active_alerts = []
        self.alert_history = deque(maxlen=100)
        
        # Thresholds
        self.thresholds = {
            'cpu_warning': 70,
            'cpu_critical': 90,
            'memory_warning': 80,
            'memory_critical': 95,
            'disk_warning': 85,
            'disk_critical': 95
        }
    
    def start_monitoring(self):
        """Start health monitoring"""
        if not self.is_monitoring:
            self.is_monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            self.logger.info("System health monitoring started")
    
    def stop_monitoring(self):
        """Stop health monitoring"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        self.logger.info("System health monitoring stopped")
    
    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                self._collect_metrics()
                self._assess_health()
                self._check_alerts()
                time.sleep(10)  # Check every 10 seconds
            except Exception as e:
                self.logger.error(f"Health monitoring error: {e}")
                time.sleep(30)  # Wait longer on error
    
    def _collect_metrics(self):
        """Collect system metrics"""
        try:
            current_time = datetime.now()
            
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            self.health_metrics['cpu_usage'].append({
                'timestamp': current_time,
                'value': cpu_percent
            })
            
            # Memory usage
            memory = psutil.virtual_memory()
            self.health_metrics['memory_usage'].append({
                'timestamp': current_time,
                'value': memory.percent,
                'available_gb': memory.available / (1024**3)
            })
            
            # Disk usage
            disk = psutil.disk_usage('/')
            self.health_metrics['disk_usage'].append({
                'timestamp': current_time,
                'value': disk.percent,
                'free_gb': disk.free / (1024**3)
            })
            
            # Network I/O
            net_io = psutil.net_io_counters()
            self.health_metrics['network_io'].append({
                'timestamp': current_time,
                'bytes_sent': net_io.bytes_sent,
                'bytes_recv': net_io.bytes_recv
            })
            
            # Process count
            process_count = len(psutil.pids())
            self.health_metrics['process_count'].append({
                'timestamp': current_time,
                'value': process_count
            })
            
            # System load average (Unix-like systems)
            try:
                load_avg = psutil.getloadavg()
                self.health_metrics['system_load'].append({
                    'timestamp': current_time,
                    'load_1m': load_avg[0],
                    'load_5m': load_avg[1],
                    'load_15m': load_avg[2]
                })
            except AttributeError:
                # Windows doesn't have load average
                pass
                
        except Exception as e:
            self.logger.error(f"Metrics collection failed: {e}")
    
    def _assess_health(self):
        """Assess overall system health"""
        try:
            current_time = datetime.now()
            
            # Assess CPU health
            if self.health_metrics['cpu_usage']:
                latest_cpu = self.health_metrics['cpu_usage'][-1]['value']
                if latest_cpu >= self.thresholds['cpu_critical']:
                    self.health_status['cpu'] = 'critical'
                elif latest_cpu >= self.thresholds['cpu_warning']:
                    self.health_status['cpu'] = 'warning'
                else:
                    self.health_status['cpu'] = 'good'
            
            # Assess memory health
            if self.health_metrics['memory_usage']:
                latest_memory = self.health_metrics['memory_usage'][-1]['value']
                if latest_memory >= self.thresholds['memory_critical']:
                    self.health_status['memory'] = 'critical'
                elif latest_memory >= self.thresholds['memory_warning']:
                    self.health_status['memory'] = 'warning'
                else:
                    self.health_status['memory'] = 'good'
            
            # Assess disk health
            if self.health_metrics['disk_usage']:
                latest_disk = self.health_metrics['disk_usage'][-1]['value']
                if latest_disk >= self.thresholds['disk_critical']:
                    self.health_status['disk'] = 'critical'
                elif latest_disk >= self.thresholds['disk_warning']:
                    self.health_status['disk'] = 'warning'
                else:
                    self.health_status['disk'] = 'good'
            
            # Assess network health (basic check)
            self.health_status['network'] = 'good'  # Simplified for now
            
            # Overall health assessment
            statuses = [
                self.health_status['cpu'],
                self.health_status['memory'],
                self.health_status['disk'],
                self.health_status['network']
            ]
            
            if 'critical' in statuses:
                self.health_status['overall'] = 'critical'
            elif 'warning' in statuses:
                self.health_status['overall'] = 'warning'
            else:
                self.health_status['overall'] = 'good'
            
            self.health_status['last_check'] = current_time
            
        except Exception as e:
            self.logger.error(f"Health assessment failed: {e}")
    
    def _check_alerts(self):
        """Check for alert conditions"""
        try:
            current_alerts = []
            
            # CPU alerts
            if self.health_status['cpu'] == 'critical':
                current_alerts.append({
                    'type': 'CPU_CRITICAL',
                    'severity': 'critical',
                    'message': f"CPU usage is critically high ({self.health_metrics['cpu_usage'][-1]['value']:.1f}%)"
                })
            elif self.health_status['cpu'] == 'warning':
                current_alerts.append({
                    'type': 'CPU_WARNING',
                    'severity': 'warning',
                    'message': f"CPU usage is high ({self.health_metrics['cpu_usage'][-1]['value']:.1f}%)"
                })
            
            # Memory alerts
            if self.health_status['memory'] == 'critical':
                current_alerts.append({
                    'type': 'MEMORY_CRITICAL',
                    'severity': 'critical',
                    'message': f"Memory usage is critically high ({self.health_metrics['memory_usage'][-1]['value']:.1f}%)"
                })
            elif self.health_status['memory'] == 'warning':
                current_alerts.append({
                    'type': 'MEMORY_WARNING',
                    'severity': 'warning',
                    'message': f"Memory usage is high ({self.health_metrics['memory_usage'][-1]['value']:.1f}%)"
                })
            
            # Disk alerts
            if self.health_status['disk'] == 'critical':
                current_alerts.append({
                    'type': 'DISK_CRITICAL',
                    'severity': 'critical',
                    'message': f"Disk usage is critically high ({self.health_metrics['disk_usage'][-1]['value']:.1f}%)"
                })
            elif self.health_status['disk'] == 'warning':
                current_alerts.append({
                    'type': 'DISK_WARNING',
                    'severity': 'warning',
                    'message': f"Disk usage is high ({self.health_metrics['disk_usage'][-1]['value']:.1f}%)"
                })
            
            # Update active alerts
            self.active_alerts = current_alerts
            
            # Log new alerts
            for alert in current_alerts:
                if not any(h['type'] == alert['type'] and 
                          (datetime.now() - h['timestamp']).seconds < 300 
                          for h in self.alert_history):
                    alert['timestamp'] = datetime.now()
                    self.alert_history.append(alert.copy())
                    self.logger.warning(f"Health Alert: {alert['message']}")
                    
        except Exception as e:
            self.logger.error(f"Alert checking failed: {e}")
    
    def get_health_report(self):
        """Get comprehensive health report"""
        try:
            report = {
                'timestamp': datetime.now(),
                'overall_status': self.health_status['overall'],
                'component_status': self.health_status.copy(),
                'current_metrics': {},
                'active_alerts': self.active_alerts.copy(),
                'recent_alerts': list(self.alert_history)[-10:],
                'recommendations': []
            }
            
            # Current metrics
            if self.health_metrics['cpu_usage']:
                report['current_metrics']['cpu_usage'] = self.health_metrics['cpu_usage'][-1]['value']
            
            if self.health_metrics['memory_usage']:
                memory_data = self.health_metrics['memory_usage'][-1]
                report['current_metrics']['memory_usage'] = memory_data['value']
                report['current_metrics']['memory_available_gb'] = memory_data['available_gb']
            
            if self.health_metrics['disk_usage']:
                disk_data = self.health_metrics['disk_usage'][-1]
                report['current_metrics']['disk_usage'] = disk_data['value']
                report['current_metrics']['disk_free_gb'] = disk_data['free_gb']
            
            # Generate recommendations
            report['recommendations'] = self._generate_recommendations()
            
            return report
            
        except Exception as e:
            self.logger.error(f"Health report generation failed: {e}")
            return {}
    
    def _generate_recommendations(self):
        """Generate health improvement recommendations"""
        recommendations = []
        
        try:
            # CPU recommendations
            if self.health_status['cpu'] in ['warning', 'critical']:
                recommendations.append({
                    'category': 'CPU',
                    'priority': 'high' if self.health_status['cpu'] == 'critical' else 'medium',
                    'message': 'High CPU usage detected',
                    'actions': [
                        'Close unnecessary applications',
                        'Reduce face recognition frequency',
                        'Use more efficient recognition model',
                        'Consider upgrading hardware'
                    ]
                })
            
            # Memory recommendations
            if self.health_status['memory'] in ['warning', 'critical']:
                recommendations.append({
                    'category': 'Memory',
                    'priority': 'high' if self.health_status['memory'] == 'critical' else 'medium',
                    'message': 'High memory usage detected',
                    'actions': [
                        'Clear application caches',
                        'Restart the application',
                        'Close other memory-intensive applications',
                        'Consider adding more RAM'
                    ]
                })
            
            # Disk recommendations
            if self.health_status['disk'] in ['warning', 'critical']:
                recommendations.append({
                    'category': 'Disk',
                    'priority': 'high' if self.health_status['disk'] == 'critical' else 'medium',
                    'message': 'Low disk space detected',
                    'actions': [
                        'Clean up old attendance records',
                        'Remove old backup files',
                        'Clear temporary files',
                        'Move data to external storage'
                    ]
                })
            
        except Exception as e:
            self.logger.error(f"Recommendation generation failed: {e}")
        
        return recommendations
    
    def run_diagnostics(self):
        """Run comprehensive system diagnostics"""
        try:
            diagnostics = {
                'timestamp': datetime.now(),
                'system_info': {},
                'performance_metrics': {},
                'health_check': {},
                'recommendations': []
            }
            
            # System information
            diagnostics['system_info'] = {
                'platform': psutil.WINDOWS if hasattr(psutil, 'WINDOWS') else 'unix',
                'cpu_count': psutil.cpu_count(),
                'cpu_freq': psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None,
                'memory_total_gb': psutil.virtual_memory().total / (1024**3),
                'disk_total_gb': psutil.disk_usage('/').total / (1024**3),
                'boot_time': datetime.fromtimestamp(psutil.boot_time())
            }
            
            # Performance metrics
            if self.health_metrics['cpu_usage']:
                cpu_values = [m['value'] for m in self.health_metrics['cpu_usage']]
                diagnostics['performance_metrics']['cpu'] = {
                    'current': cpu_values[-1],
                    'average': sum(cpu_values) / len(cpu_values),
                    'max': max(cpu_values),
                    'min': min(cpu_values)
                }
            
            if self.health_metrics['memory_usage']:
                memory_values = [m['value'] for m in self.health_metrics['memory_usage']]
                diagnostics['performance_metrics']['memory'] = {
                    'current': memory_values[-1],
                    'average': sum(memory_values) / len(memory_values),
                    'max': max(memory_values),
                    'min': min(memory_values)
                }
            
            # Health check
            diagnostics['health_check'] = self.health_status.copy()
            
            # Recommendations
            diagnostics['recommendations'] = self._generate_recommendations()
            
            return diagnostics
            
        except Exception as e:
            self.logger.error(f"Diagnostics failed: {e}")
            return {}

# Global health monitor instance
system_health_monitor = SystemHealthMonitor()
