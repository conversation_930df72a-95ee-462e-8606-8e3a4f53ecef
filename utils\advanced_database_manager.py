"""
Advanced Database Manager for Face Recognition Attendance System
Enterprise-level database operations with connection pooling, transactions, and monitoring
"""

import sqlite3
import threading
import time
import json
from datetime import datetime, timedelta
from collections import defaultdict, deque
from contextlib import contextmanager
import config
from utils.database_manager import DatabaseManager
from utils.logger import setup_logger

class AdvancedDatabaseManager(DatabaseManager):
    """Advanced database manager with enterprise features"""
    
    def __init__(self):
        super().__init__()
        self.logger = setup_logger("AdvancedDatabaseManager")
        
        # Connection pooling
        self.connection_pool = deque()
        self.pool_size = 10
        self.pool_lock = threading.RLock()
        self.active_connections = set()
        
        # Transaction management
        self.transaction_lock = threading.RLock()
        self.active_transactions = {}
        
        # Performance monitoring
        self.query_stats = defaultdict(lambda: {
            'count': 0,
            'total_time': 0.0,
            'avg_time': 0.0,
            'errors': 0,
            'last_executed': None
        })
        
        # Query cache
        self.query_cache = {}
        self.cache_lock = threading.RLock()
        self.cache_max_size = 1000
        self.cache_ttl = 300  # 5 minutes
        
        # Data validation
        self.validation_rules = self._setup_validation_rules()
        
        # Performance optimization
        self.batch_size = 1000
        self.enable_wal_mode = True
        
        # Initialize advanced features
        self._initialize_advanced_features()
    
    def _initialize_advanced_features(self):
        """Initialize advanced database features"""
        try:
            # Initialize connection pool
            self._initialize_connection_pool()
            
            # Enable WAL mode for better concurrency
            if self.enable_wal_mode:
                self._enable_wal_mode()
            
            # Create performance monitoring tables
            self._create_monitoring_tables()
            
            # Set up database optimization
            self._optimize_database_settings()
            
            self.logger.info("Advanced database features initialized")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize advanced features: {e}")
    
    def _initialize_connection_pool(self):
        """Initialize database connection pool"""
        try:
            with self.pool_lock:
                for _ in range(self.pool_size):
                    conn = self._create_connection()
                    self.connection_pool.append(conn)
            
            self.logger.info(f"Connection pool initialized with {self.pool_size} connections")
            
        except Exception as e:
            self.logger.error(f"Connection pool initialization failed: {e}")
    
    def _create_connection(self):
        """Create a new database connection with optimizations"""
        try:
            conn = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=30.0,
                isolation_level=None  # Autocommit mode
            )
            conn.row_factory = sqlite3.Row
            
            # Set pragmas for performance
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA cache_size=10000")
            conn.execute("PRAGMA temp_store=MEMORY")
            conn.execute("PRAGMA mmap_size=268435456")  # 256MB
            
            return conn
            
        except Exception as e:
            self.logger.error(f"Failed to create connection: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """Get connection from pool with context manager"""
        conn = None
        try:
            with self.pool_lock:
                if self.connection_pool:
                    conn = self.connection_pool.popleft()
                    self.active_connections.add(conn)
                else:
                    # Pool exhausted, create new connection
                    conn = self._create_connection()
                    self.active_connections.add(conn)
            
            yield conn
            
        except Exception as e:
            self.logger.error(f"Connection error: {e}")
            if conn:
                # Connection might be corrupted, don't return to pool
                try:
                    conn.close()
                except:
                    pass
                conn = None
            raise
        finally:
            if conn:
                try:
                    with self.pool_lock:
                        self.active_connections.discard(conn)
                        if len(self.connection_pool) < self.pool_size:
                            self.connection_pool.append(conn)
                        else:
                            conn.close()
                except Exception as e:
                    self.logger.error(f"Error returning connection to pool: {e}")
    
    def _enable_wal_mode(self):
        """Enable WAL mode for better concurrency"""
        try:
            with self.get_connection() as conn:
                conn.execute("PRAGMA journal_mode=WAL")
                result = conn.execute("PRAGMA journal_mode").fetchone()
                if result and result[0] == 'wal':
                    self.logger.info("WAL mode enabled successfully")
                else:
                    self.logger.warning("Failed to enable WAL mode")
                    
        except Exception as e:
            self.logger.error(f"WAL mode setup failed: {e}")
    
    def _create_monitoring_tables(self):
        """Create tables for performance monitoring"""
        try:
            with self.get_connection() as conn:
                # Query performance log
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS query_performance_log (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        query_type TEXT NOT NULL,
                        execution_time REAL NOT NULL,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        success BOOLEAN DEFAULT 1,
                        error_message TEXT
                    )
                ''')
                
                # System metrics log
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS system_metrics_log (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        metric_name TEXT NOT NULL,
                        metric_value REAL NOT NULL,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Data integrity log
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS data_integrity_log (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        table_name TEXT NOT NULL,
                        operation TEXT NOT NULL,
                        record_id INTEGER,
                        old_values TEXT,
                        new_values TEXT,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        user_id TEXT
                    )
                ''')
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"Monitoring tables creation failed: {e}")
    
    def _optimize_database_settings(self):
        """Optimize database settings for performance"""
        try:
            with self.get_connection() as conn:
                # Create indexes for better performance
                indexes = [
                    "CREATE INDEX IF NOT EXISTS idx_students_roll_number ON students(roll_number)",
                    "CREATE INDEX IF NOT EXISTS idx_students_class ON students(class_name, section)",
                    "CREATE INDEX IF NOT EXISTS idx_students_active ON students(is_active)",
                    "CREATE INDEX IF NOT EXISTS idx_attendance_date ON attendance(attendance_date)",
                    "CREATE INDEX IF NOT EXISTS idx_attendance_student ON attendance(student_id)",
                    "CREATE INDEX IF NOT EXISTS idx_attendance_student_date ON attendance(student_id, attendance_date)",
                    "CREATE INDEX IF NOT EXISTS idx_attendance_time ON attendance(attendance_time)",
                    "CREATE INDEX IF NOT EXISTS idx_admin_users_username ON admin_users(username)",
                    "CREATE INDEX IF NOT EXISTS idx_system_settings_key ON system_settings(setting_key)"
                ]
                
                for index_sql in indexes:
                    conn.execute(index_sql)
                
                # Analyze tables for query optimization
                conn.execute("ANALYZE")
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"Database optimization failed: {e}")
    
    def _setup_validation_rules(self):
        """Setup data validation rules"""
        return {
            'students': {
                'roll_number': {
                    'required': True,
                    'type': str,
                    'max_length': 20,
                    'pattern': r'^[A-Za-z0-9]+$'
                },
                'full_name': {
                    'required': True,
                    'type': str,
                    'max_length': 100,
                    'min_length': 2
                },
                'class_name': {
                    'required': True,
                    'type': str,
                    'max_length': 50
                },
                'email': {
                    'required': False,
                    'type': str,
                    'pattern': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
                },
                'phone': {
                    'required': False,
                    'type': str,
                    'pattern': r'^\+?[\d\s\-\(\)]{10,15}$'
                }
            },
            'attendance': {
                'student_id': {
                    'required': True,
                    'type': int
                },
                'attendance_date': {
                    'required': True,
                    'type': str
                },
                'confidence_score': {
                    'required': False,
                    'type': float,
                    'min_value': 0.0,
                    'max_value': 100.0
                }
            }
        }
    
    def validate_data(self, table_name, data):
        """Validate data against rules"""
        try:
            if table_name not in self.validation_rules:
                return True, "No validation rules defined"
            
            rules = self.validation_rules[table_name]
            errors = []
            
            for field, rule in rules.items():
                value = data.get(field)
                
                # Check required fields
                if rule.get('required', False) and (value is None or value == ''):
                    errors.append(f"{field} is required")
                    continue
                
                if value is None or value == '':
                    continue
                
                # Check type
                expected_type = rule.get('type')
                if expected_type and not isinstance(value, expected_type):
                    try:
                        # Try to convert
                        if expected_type == int:
                            value = int(value)
                        elif expected_type == float:
                            value = float(value)
                        elif expected_type == str:
                            value = str(value)
                        data[field] = value
                    except (ValueError, TypeError):
                        errors.append(f"{field} must be of type {expected_type.__name__}")
                        continue
                
                # Check string length
                if isinstance(value, str):
                    if 'max_length' in rule and len(value) > rule['max_length']:
                        errors.append(f"{field} exceeds maximum length of {rule['max_length']}")
                    if 'min_length' in rule and len(value) < rule['min_length']:
                        errors.append(f"{field} is below minimum length of {rule['min_length']}")
                
                # Check numeric ranges
                if isinstance(value, (int, float)):
                    if 'min_value' in rule and value < rule['min_value']:
                        errors.append(f"{field} is below minimum value of {rule['min_value']}")
                    if 'max_value' in rule and value > rule['max_value']:
                        errors.append(f"{field} exceeds maximum value of {rule['max_value']}")
                
                # Check patterns
                if 'pattern' in rule and isinstance(value, str):
                    import re
                    if not re.match(rule['pattern'], value):
                        errors.append(f"{field} format is invalid")
            
            if errors:
                return False, "; ".join(errors)
            
            return True, "Validation passed"
            
        except Exception as e:
            self.logger.error(f"Data validation error: {e}")
            return False, f"Validation error: {str(e)}"
    
    def execute_query(self, query, params=None, query_type="unknown"):
        """Execute query with performance monitoring"""
        start_time = time.time()
        success = True
        error_message = None
        result = None
        
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                if query.strip().upper().startswith('SELECT'):
                    result = cursor.fetchall()
                else:
                    conn.commit()
                    result = cursor.rowcount
                
        except Exception as e:
            success = False
            error_message = str(e)
            self.logger.error(f"Query execution failed: {e}")
            raise
        
        finally:
            # Update performance stats
            execution_time = time.time() - start_time
            self._update_query_stats(query_type, execution_time, success, error_message)
        
        return result
    
    def _update_query_stats(self, query_type, execution_time, success, error_message):
        """Update query performance statistics"""
        try:
            stats = self.query_stats[query_type]
            stats['count'] += 1
            stats['total_time'] += execution_time
            stats['avg_time'] = stats['total_time'] / stats['count']
            stats['last_executed'] = datetime.now()
            
            if not success:
                stats['errors'] += 1
            
            # Log to performance table (async to avoid recursion)
            threading.Thread(
                target=self._log_query_performance,
                args=(query_type, execution_time, success, error_message),
                daemon=True
            ).start()
            
        except Exception as e:
            self.logger.error(f"Query stats update failed: {e}")
    
    def _log_query_performance(self, query_type, execution_time, success, error_message):
        """Log query performance to database"""
        try:
            with self.get_connection() as conn:
                conn.execute('''
                    INSERT INTO query_performance_log 
                    (query_type, execution_time, success, error_message)
                    VALUES (?, ?, ?, ?)
                ''', (query_type, execution_time, success, error_message))
                conn.commit()
                
        except Exception as e:
            # Don't log this error to avoid recursion
            pass
    
    def add_student_advanced(self, student_data, face_encoding=None):
        """Add student with advanced validation and monitoring"""
        try:
            # Validate data
            valid, message = self.validate_data('students', student_data)
            if not valid:
                return False, f"Validation failed: {message}"
            
            # Check for duplicates
            existing = self.get_student_by_roll_number(student_data['roll_number'])
            if existing:
                return False, f"Student with roll number {student_data['roll_number']} already exists"
            
            # Add student using parent method
            student_id = self.add_student(student_data, face_encoding)
            
            # Log data integrity
            self._log_data_integrity('students', 'INSERT', student_id, None, student_data)
            
            return True, f"Student added successfully with ID: {student_id}"
            
        except Exception as e:
            self.logger.error(f"Advanced student addition failed: {e}")
            return False, f"Failed to add student: {str(e)}"
    
    def update_student_advanced(self, student_id, student_data, face_encoding=None):
        """Update student with validation and audit trail"""
        try:
            # Get current data for audit trail
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM students WHERE id = ?", (student_id,))
                old_data = cursor.fetchone()
                
                if not old_data:
                    return False, "Student not found"
                
                old_data = dict(old_data)
            
            # Validate new data
            valid, message = self.validate_data('students', student_data)
            if not valid:
                return False, f"Validation failed: {message}"
            
            # Update using parent method
            success = self.update_student(student_id, student_data, face_encoding)
            
            if success:
                # Log data integrity
                self._log_data_integrity('students', 'UPDATE', student_id, old_data, student_data)
                return True, "Student updated successfully"
            else:
                return False, "Update failed"
                
        except Exception as e:
            self.logger.error(f"Advanced student update failed: {e}")
            return False, f"Failed to update student: {str(e)}"
    
    def _log_data_integrity(self, table_name, operation, record_id, old_values, new_values):
        """Log data changes for audit trail"""
        try:
            with self.get_connection() as conn:
                conn.execute('''
                    INSERT INTO data_integrity_log 
                    (table_name, operation, record_id, old_values, new_values)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    table_name,
                    operation,
                    record_id,
                    json.dumps(old_values, default=str) if old_values else None,
                    json.dumps(new_values, default=str) if new_values else None
                ))
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"Data integrity logging failed: {e}")
    
    def add_attendance_record(self, attendance_data):
        """Add attendance record with validation"""
        try:
            # Validate data
            valid, message = self.validate_data('attendance', attendance_data)
            if not valid:
                return False, f"Validation failed: {message}"
            
            # Use parent method
            success, msg = self.mark_attendance(
                attendance_data['student_id'],
                attendance_data.get('confidence_score')
            )
            
            if success:
                # Log data integrity
                self._log_data_integrity('attendance', 'INSERT', None, None, attendance_data)
            
            return success, msg
            
        except Exception as e:
            self.logger.error(f"Attendance record addition failed: {e}")
            return False, f"Failed to add attendance: {str(e)}"
    
    def get_student_attendance_by_date(self, student_id, attendance_date):
        """Get student attendance for specific date"""
        try:
            query = '''
                SELECT * FROM attendance 
                WHERE student_id = ? AND attendance_date = ?
            '''
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, (student_id, attendance_date))
                return cursor.fetchone()
                
        except Exception as e:
            self.logger.error(f"Get student attendance failed: {e}")
            return None
    
    def get_performance_report(self):
        """Get comprehensive database performance report"""
        try:
            report = {
                'connection_pool': {
                    'pool_size': self.pool_size,
                    'available_connections': len(self.connection_pool),
                    'active_connections': len(self.active_connections)
                },
                'query_statistics': dict(self.query_stats),
                'cache_statistics': {
                    'cache_size': len(self.query_cache),
                    'cache_max_size': self.cache_max_size,
                    'cache_hit_rate': self._calculate_cache_hit_rate()
                },
                'database_size': self._get_database_size(),
                'table_statistics': self._get_table_statistics()
            }
            
            return report
            
        except Exception as e:
            self.logger.error(f"Performance report generation failed: {e}")
            return {}
    
    def _calculate_cache_hit_rate(self):
        """Calculate cache hit rate"""
        # Simplified implementation
        return 0.0
    
    def _get_database_size(self):
        """Get database file size"""
        try:
            import os
            return os.path.getsize(self.db_path)
        except Exception:
            return 0
    
    def _get_table_statistics(self):
        """Get table row counts and sizes"""
        try:
            stats = {}
            tables = ['students', 'attendance', 'admin_users', 'system_settings']
            
            with self.get_connection() as conn:
                for table in tables:
                    cursor = conn.cursor()
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    stats[table] = {'row_count': count}
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Table statistics failed: {e}")
            return {}
    
    def cleanup_old_logs(self, days_to_keep=30):
        """Clean up old performance and integrity logs"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            with self.get_connection() as conn:
                # Clean performance logs
                conn.execute('''
                    DELETE FROM query_performance_log 
                    WHERE timestamp < ?
                ''', (cutoff_date,))
                
                # Clean system metrics logs
                conn.execute('''
                    DELETE FROM system_metrics_log 
                    WHERE timestamp < ?
                ''', (cutoff_date,))
                
                # Clean old integrity logs (keep more for audit)
                audit_cutoff = datetime.now() - timedelta(days=days_to_keep * 3)
                conn.execute('''
                    DELETE FROM data_integrity_log 
                    WHERE timestamp < ?
                ''', (audit_cutoff,))
                
                conn.commit()
                
            self.logger.info(f"Cleaned up logs older than {days_to_keep} days")
            
        except Exception as e:
            self.logger.error(f"Log cleanup failed: {e}")
    
    def close_all_connections(self):
        """Close all connections in pool"""
        try:
            with self.pool_lock:
                # Close pooled connections
                while self.connection_pool:
                    conn = self.connection_pool.popleft()
                    try:
                        conn.close()
                    except:
                        pass
                
                # Close active connections
                for conn in list(self.active_connections):
                    try:
                        conn.close()
                    except:
                        pass
                
                self.active_connections.clear()
            
            # Close parent connection
            super().close_connection()
            
            self.logger.info("All database connections closed")
            
        except Exception as e:
            self.logger.error(f"Error closing connections: {e}")
