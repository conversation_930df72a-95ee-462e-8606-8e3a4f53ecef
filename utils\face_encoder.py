"""
Face Encoding Utility for Face Recognition Attendance System
Handles face detection, encoding, and image processing
"""

import cv2
import face_recognition
import numpy as np
from PIL import Image, ImageTk
import os
from pathlib import Path
import config
from utils.logger import log_face_recognition_event, setup_logger

class FaceEncoder:
    """Handles face encoding operations"""
    
    def __init__(self):
        self.logger = setup_logger("FaceEncoder")
        self.model = config.MODEL
        self.tolerance = config.TOLERANCE
        
    def detect_faces_in_image(self, image_path_or_array):
        """
        Detect faces in an image
        
        Args:
            image_path_or_array: Path to image file or numpy array
            
        Returns:
            tuple: (face_locations, face_encodings)
        """
        try:
            # Load image
            if isinstance(image_path_or_array, str):
                image = face_recognition.load_image_file(image_path_or_array)
            else:
                image = image_path_or_array
            
            # Find face locations
            face_locations = face_recognition.face_locations(image, model=self.model)
            
            # Generate face encodings
            face_encodings = face_recognition.face_encodings(image, face_locations)
            
            log_face_recognition_event("DETECTION", {
                "faces_found": len(face_locations),
                "model": self.model
            })
            
            return face_locations, face_encodings
            
        except Exception as e:
            self.logger.error(f"Face detection failed: {str(e)}")
            return [], []
    
    def encode_face_from_image(self, image_path_or_array):
        """
        Generate face encoding from image
        
        Args:
            image_path_or_array: Path to image file or numpy array
            
        Returns:
            numpy.ndarray or None: Face encoding if successful
        """
        try:
            face_locations, face_encodings = self.detect_faces_in_image(image_path_or_array)
            
            if len(face_encodings) == 0:
                self.logger.warning("No faces found in image")
                return None
            
            if len(face_encodings) > 1:
                self.logger.warning(f"Multiple faces found ({len(face_encodings)}), using the first one")
            
            encoding = face_encodings[0]
            
            log_face_recognition_event("ENCODING", {
                "encoding_size": len(encoding),
                "success": True
            })
            
            return encoding
            
        except Exception as e:
            self.logger.error(f"Face encoding failed: {str(e)}")
            log_face_recognition_event("ENCODING", {
                "success": False,
                "error": str(e)
            })
            return None
    
    def encode_face_from_webcam_frame(self, frame):
        """
        Generate face encoding from webcam frame
        
        Args:
            frame: OpenCV frame (BGR format)
            
        Returns:
            tuple: (face_encoding, face_location) or (None, None)
        """
        try:
            # Convert BGR to RGB
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # Find face locations and encodings
            face_locations = face_recognition.face_locations(rgb_frame, model=self.model)
            face_encodings = face_recognition.face_encodings(rgb_frame, face_locations)
            
            if len(face_encodings) == 0:
                return None, None
            
            # Return first face found
            return face_encodings[0], face_locations[0]
            
        except Exception as e:
            self.logger.error(f"Webcam face encoding failed: {str(e)}")
            return None, None
    
    def save_face_image(self, frame, face_location, student_roll_number):
        """
        Save cropped face image
        
        Args:
            frame: OpenCV frame
            face_location: Face location tuple (top, right, bottom, left)
            student_roll_number: Student's roll number
            
        Returns:
            str: Path to saved image or None
        """
        try:
            # Create student directory
            student_dir = config.FACES_DIR / student_roll_number
            student_dir.mkdir(exist_ok=True)
            
            # Extract face coordinates
            top, right, bottom, left = face_location
            
            # Crop face from frame
            face_image = frame[top:bottom, left:right]
            
            # Generate filename with timestamp
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"face_{timestamp}.jpg"
            file_path = student_dir / filename
            
            # Save image
            cv2.imwrite(str(file_path), face_image)
            
            self.logger.info(f"Face image saved: {file_path}")
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"Failed to save face image: {str(e)}")
            return None
    
    def preprocess_image(self, image_path, target_size=(640, 480)):
        """
        Preprocess image for better face recognition
        
        Args:
            image_path: Path to image file
            target_size: Target image size (width, height)
            
        Returns:
            numpy.ndarray: Preprocessed image
        """
        try:
            # Load image
            image = cv2.imread(image_path)
            
            if image is None:
                raise ValueError(f"Could not load image: {image_path}")
            
            # Resize image
            image = cv2.resize(image, target_size)
            
            # Enhance contrast
            lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
            l, a, b = cv2.split(lab)
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            l = clahe.apply(l)
            enhanced = cv2.merge([l, a, b])
            image = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)
            
            # Reduce noise
            image = cv2.bilateralFilter(image, 9, 75, 75)
            
            return image
            
        except Exception as e:
            self.logger.error(f"Image preprocessing failed: {str(e)}")
            return None
    
    def validate_face_quality(self, face_encoding, face_location, frame_shape):
        """
        Validate face quality for registration
        
        Args:
            face_encoding: Face encoding array
            face_location: Face location tuple
            frame_shape: Frame dimensions
            
        Returns:
            tuple: (is_valid, quality_score, issues)
        """
        issues = []
        quality_score = 100
        
        try:
            # Check if encoding exists
            if face_encoding is None:
                return False, 0, ["No face encoding generated"]
            
            # Check face size
            top, right, bottom, left = face_location
            face_width = right - left
            face_height = bottom - top
            frame_height, frame_width = frame_shape[:2]
            
            # Face should be at least 10% of frame width
            min_face_size = frame_width * 0.1
            if face_width < min_face_size or face_height < min_face_size:
                issues.append("Face too small")
                quality_score -= 30
            
            # Face should not be too large (more than 80% of frame)
            max_face_size = frame_width * 0.8
            if face_width > max_face_size or face_height > max_face_size:
                issues.append("Face too large")
                quality_score -= 20
            
            # Check face position (should be reasonably centered)
            face_center_x = (left + right) / 2
            face_center_y = (top + bottom) / 2
            frame_center_x = frame_width / 2
            frame_center_y = frame_height / 2
            
            # Allow 30% deviation from center
            max_deviation_x = frame_width * 0.3
            max_deviation_y = frame_height * 0.3
            
            if abs(face_center_x - frame_center_x) > max_deviation_x:
                issues.append("Face not horizontally centered")
                quality_score -= 15
            
            if abs(face_center_y - frame_center_y) > max_deviation_y:
                issues.append("Face not vertically centered")
                quality_score -= 15
            
            # Check encoding quality (face_recognition library provides 128-dimensional encodings)
            if len(face_encoding) != 128:
                issues.append("Invalid face encoding")
                quality_score -= 50
            
            # Check for extreme values in encoding (might indicate poor quality)
            if np.any(np.abs(face_encoding) > 1.5):
                issues.append("Face encoding has extreme values")
                quality_score -= 20
            
            is_valid = quality_score >= 50 and len(issues) == 0
            
            return is_valid, max(0, quality_score), issues
            
        except Exception as e:
            self.logger.error(f"Face quality validation failed: {str(e)}")
            return False, 0, [f"Validation error: {str(e)}"]
    
    def create_face_thumbnail(self, image_path, output_path, size=(150, 150)):
        """
        Create thumbnail of face image
        
        Args:
            image_path: Path to original image
            output_path: Path to save thumbnail
            size: Thumbnail size (width, height)
            
        Returns:
            bool: Success status
        """
        try:
            # Load and resize image
            image = Image.open(image_path)
            image.thumbnail(size, Image.Resampling.LANCZOS)
            
            # Save thumbnail
            image.save(output_path, "JPEG", quality=85)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Thumbnail creation failed: {str(e)}")
            return False
